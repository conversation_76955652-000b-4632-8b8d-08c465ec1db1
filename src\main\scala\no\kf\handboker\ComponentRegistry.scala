package no.kf.handboker

import com.sksamuel.elastic4s.ElasticClient
import no.kf.config.{MockBrukerAdm, Settings}
import no.kf.db.{DbConnectionManager, ExportAndDeleteRepository, ExportAndDeleteRepositoryComponent}
import no.kf.exception.KfException
import no.kf.handboker.config.{AppSettingComponent, LogStore, MockElasticSearch}
import no.kf.handboker.repository._
import no.kf.handboker.repository.welcomepage.{WelcomePageRepository, WelcomePageRepositoryComponent}
import no.kf.handboker.service._
import no.kf.handboker.service.welcomepage.{WelcomePageService, WelcomePageServiceComponent}
import no.kf.handboker.service.api.{HandbookApiService, HandbookApiServiceComponent}
import no.kf.handboker.service.handbookdownload.{ReportService, ReportServiceComponent}
import no.kf.handboker.service.search._
import no.kf.util.FileSystemUtil._
import no.kf.util.Logging
import org.apache.logging.log4j.LogManager

class ComponentRegistry
  extends AppSettingComponent
  with DbConnectionManagerComponent
  with LDAPServiceComponent
  with HealthCheckServiceComponent
  with LocalHandbookServiceComponent
  with SubscriptionServiceComponent
  with HandbookSynchronizationServiceComponent
  with CentralHandbookServiceComponent
  with CentralHandbookPublicationServiceComponent
  with CentralAccessServiceComponent
  with CentralAccessRepositoryComponent
  with HandbookRepositoryComponent
  with CentralHandbookRepositoryComponent
  with SubscriptionRepositoryComponent
  with ImageServiceComponent
  with FileServiceComponent
  with MailServiceComponent
  with ElasticClientManagerServiceComponent
  with SearchServiceComponent
  with SearchIndexServiceComponent
  with SearchIndexBuilderServiceComponent
  with ExternalOrganizationServiceComponent
  with HandbookApiServiceComponent
  with GlobalSettingsApiServiceComponent
  with ExportAndDeleteRepositoryComponent
  with OrganizationDataServiceComponent
  with HandbookLinkRepositoryComponent
  with HandbookLinkServiceComponent
  with ReportServiceComponent
  with PublicationRepositoryComponent
  with CentralNotificationRepositoryComponent
  with ReadingLinkRepositoryComponent
  with ReadingLinkServiceComponent
  with LocalHandbookVersionRepositoryComponent
  with LocalHandbookVersionServiceComponent
  with WelcomePageRepositoryComponent
  with WelcomePageServiceComponent
  with Logging
  with FileLinkRepositoryComponent
  with FileLinkServiceComponent {

  lazy val connectionManager: DbConnectionManager = new DbConnectionManagerImpl
  lazy val ldapService: LDAPService = new LDAPServiceImpl
  lazy val healthCheckService: HealthCheckService = new HealthCheckServiceImpl
  lazy val localHandbookService: LocalHandbookService = new LocalHandbookServiceImpl
  lazy val centralHandbookService: CentralHandbookService = new CentralHandbookServiceImpl
  lazy val handbookSynchronizationService: HandbookSynchronizationService = new HandbookSynchronizationServiceImpl
  lazy val subscriptionService: SubscriptionService = new SubscriptionServiceImpl
  lazy val centralAccessService: CentralAccessService = new CentralAccessServiceImpl
  lazy val centralAccessRepository: CentralAccessRepository = new CentralAccessRepositoryImpl
  lazy val handbookRepository: HandbookRepository = new HandbookRepositoryImpl
  lazy val centralHandbookRepository: CentralHandbookRepository = new CentralHandbookRepositoryImpl
  lazy val subscriptionRepository: SubscriptionRepository = new SubscriptionRepositoryImpl
  lazy val centralHandbookPublicationService: CentralHandbookPublicationService = new CentralHandbookPublicationServiceImpl
  lazy val imageService: ImageService = new ImageServiceImpl
  lazy val fileService: FileService = new FileServiceImpl
  lazy val mailService: MailService = new MailService
  lazy val externalOrganizationService: ExternalOrganizationService = new ExternalOrganizationServiceImpl
  lazy val handbookApiService: HandbookApiService = new HandbookApiServiceImpl
  lazy val handbookLinkRepository: HandbookLinkRepository = new HandbookLinkRepositoryImpl
  lazy val fileLinkRepository: FileLinkRepository = new FileLinkRepositoryImpl
  lazy val handbookLinkService: HandbookLinkService = new HandbookLinkServiceImpl
  lazy val fileLinkService: FileLinkService = new FileLinkServiceImpl
  lazy val readingLinkRepository: ReadingLinkRepository = new ReadingLinkRepositoryImpl
  lazy val readingLinkService: ReadingLinkService = new ReadingLinkServiceImpl
  lazy val localHandbookVersionRepository: LocalHandbookVersionRepository = new LocalHandbookVersionRepositoryImpl
    lazy val localHandbookVersionService: LocalHandbookVersionService = new LocalHandbookVersionServiceImpl

  lazy val welcomePageRepository: WelcomePageRepository = new WelcomePageRepositoryImpl
  lazy val welcomePageService: WelcomePageService = new WelcomePageServiceImpl

  lazy val settings: Settings = new AppSettings(getConfigDir("handboker"))
  lazy val globalSettingsApiService: GlobalSettingsApiService = new GlobalSettingsApiServiceImpl

  lazy val elasticClient: ElasticClient = ElasticClientManagerServiceImpl()
  lazy val mockElasticSearch: Boolean = settings.settingFor(MockElasticSearch).toBoolean

  lazy val searchService: SearchService = if (mockElasticSearch) new SearchMockService else new SearchServiceImpl
  lazy val searchIndexService: SearchIndexService = if (mockElasticSearch) new SearchMockService else new SearchIndexServiceImpl
  lazy val searchIndexBuilderService: SearchIndexBuilderService = if (mockElasticSearch) new SearchMockService else new SearchIndexBuilderServiceImpl

  lazy val exportAndDeleteRepository: ExportAndDeleteRepository = new ExportAndDeleteRepositoryImpl
  lazy val organizationDataService: OrganizationDataService = new OrganizationDataServiceImpl

  lazy val reportService: ReportService = new ReportServiceImpl

  lazy val publicationRepository: PublicationRepository = new PublicationRepositoryImpl
  lazy val centralNotificationRepository: CentralNotificationRepository = new CentralNotificationRepositoryImpl
}

class ProductionRegistry extends ComponentRegistry with Logging {

  {
    // Quick hack to programmatically set the log location. See https://stackoverflow.com/a/14877698 and log4j2.xml
    LogManager.getContext(false) match {
      case context: org.apache.logging.log4j.core.LoggerContext => {
        val logFileDir = getLogfileDir(settings.optionalSettingFor(LogStore), "handboker")
        // Set system property that will be read in log4j2.xml when we reconfigure
        System.setProperty("logFileDir", logFileDir)
        context.reconfigure()
      }
      case _ => log.error("Unable to configure log directory. Using default, which is probably the app server.")
    }
  }

  System.setProperty("xr.util-logging.loggingEnabled", "false")

  // Initialize cake on startup
  val services = settings ::
    connectionManager ::
    ldapService ::
    healthCheckService ::
    localHandbookService ::
    centralHandbookService ::
    handbookSynchronizationService ::
    centralHandbookPublicationService ::
    centralAccessService ::
    centralAccessRepository ::
    handbookRepository ::
    centralHandbookRepository ::
    imageService ::
    fileService ::
    mailService ::
    subscriptionService ::
    searchService ::
    searchIndexService ::
    searchIndexBuilderService ::
    externalOrganizationService ::
    handbookApiService ::
    globalSettingsApiService ::
    exportAndDeleteRepository ::
    handbookLinkRepository ::
    fileLinkRepository ::
    handbookLinkService ::
    reportService ::
    publicationRepository ::
    centralNotificationRepository ::
    readingLinkRepository ::
    readingLinkService ::
    localHandbookVersionRepository ::
    localHandbookVersionService ::
    welcomePageService ::
    fileLinkService ::
    Nil

  if (mockElasticSearch) {
    log.warn("Mocking ElasticSearch. Will be unable to perform searches")
  } else {
    // Only init this if we aren't mocking... as it tries to connect to ElasticSearch on instantiation
    elasticClient
  }


  if (!settings.settingFor(MockBrukerAdm).toBoolean) {
    ldapService.testConnection()
  }

  log.info("Wiring app using " + getClass.getName + " finished")
}

object ProductionRegistry extends Logging {
  lazy val componentRegistry: ComponentRegistry = {
    try {
      log.debug("Wiring up application")
      new ProductionRegistry
    } catch {
      case ex: Throwable =>
        val message = if (ex.isInstanceOf[ExceptionInInitializerError]) ex.getCause.getMessage else ex.getMessage
        log.error("Feil i oppstart", ex)
        log.warn("Applikasjonen kan ikke startes da konfigurasjonen er feil. " + message + " Se tidligere meldinger.")
        throw new KfException("Application is shutting down")
    }
  }
}