package no.kf.handboker.service.welcomepage

import no.kf.db.TransactionManager
import no.kf.handboker.model.{Image, welcomepage}
import no.kf.handboker.model.welcomepage._
import no.kf.handboker.repository.welcomepage.WelcomePageRepositoryComponent
import no.kf.handboker.service.ImageServiceComponent
import org.joda.time.DateTime
import java.util.Base64
import no.kf.handboker.config.{WelcomePageDefaultImageUrl, WelcomePageDefaultImageTitle, WelcomePageDefaultImageAltTitle, WelcomePageDefaultImageCredits}

trait WelcomePageServiceComponent extends TransactionManager {
  this: WelcomePageRepositoryComponent with ImageServiceComponent with no.kf.handboker.config.AppSettingComponent =>

  val welcomePageService: WelcomePageService

  class WelcomePageServiceImpl extends WelcomePageService {

    private def getWelcomePageDto(version: WelcomePageVersion): WelcomePageDto = {
      val customizationFromDb = welcomePageRepository.findCustomization(version.id).getOrElse(
        throw new IllegalStateException(s"Data inconsistency: No customization found for version ${version.id}")
      )

      // Enrich customization with base64 image data (data URI) if welcomeImage URL exists
      val enrichedCustomization = customizationFromDb.welcomeImageUrl.flatMap { path =>
        val maybeFilename = path.split("/").lastOption
        maybeFilename.flatMap { filename =>
          try {
            val file = imageService.retrieveImage(filename)
            val bytes = file.byteArray
            val contentType = file.contentType.getOrElse {
              val ext = filename.split("\\.").lastOption.getOrElse("png")
              s"image/$ext"
            }
            val base64 = Base64.getEncoder.encodeToString(bytes)
            Some(customizationFromDb.copy(welcomeImageResponse = Some(s"data:$contentType;base64,$base64")))
          } catch {
            case _: Throwable => None
          }
        }
      }.orElse {
        // Fallback to configured default welcome image if provided in settings
        try {
          val maybeUrl = settings.optionalSettingFor(WelcomePageDefaultImageUrl)
          maybeUrl.flatMap { url =>
            url.split("/").lastOption.flatMap { fname =>
              try {
                val file = imageService.retrieveImage(fname)
                val bytes = file.byteArray
                val contentType = file.contentType.getOrElse {
                  val ext = fname.split("\\.").lastOption.getOrElse("png")
                  s"image/$ext"
                }
                val base64 = Base64.getEncoder.encodeToString(bytes)
                Some(customizationFromDb.copy(
                  welcomeImageResponse = Some(s"data:$contentType;base64,$base64"),
                  imageTitle = customizationFromDb.imageTitle.orElse(settings.optionalSettingFor(WelcomePageDefaultImageTitle)),
                  altTitle = customizationFromDb.altTitle.orElse(settings.optionalSettingFor(WelcomePageDefaultImageAltTitle)),
                  imageCredits = customizationFromDb.imageCredits.orElse(settings.optionalSettingFor(WelcomePageDefaultImageCredits))
                ))
              } catch { case _: Throwable => None }
            }
          }
        } catch { case _: Throwable => None }
      }.orElse {
        // Final fallback to packaged resource (keeps old behavior if no configured default)
        try {
          val stream = getClass.getClassLoader.getResourceAsStream("coverpages/default-cover-page.png")
          if (stream != null) {
            val baos = new java.io.ByteArrayOutputStream()
            val buffer = new Array[Byte](4096)
            var n = stream.read(buffer)
            while (n != -1) {
              baos.write(buffer, 0, n)
              n = stream.read(buffer)
            }
            stream.close()
            val bytes = baos.toByteArray
            val base64 = Base64.getEncoder.encodeToString(bytes)
            Some(customizationFromDb.copy(
              welcomeImageResponse = Some(s"data:image/png;base64,$base64")
            ))
          } else None
        } catch { case _: Throwable => None }
      }.getOrElse(customizationFromDb)

      val linkCollections = welcomePageRepository.findLinkCollections(version.id)
      val linkCollectionDtos = linkCollections.map { coll =>
        val links = welcomePageRepository.findLinks(coll.id)
        WelcomePageLinkCollectionDto(coll.id, coll.title, coll.sortOrder, links, coll.lastUpdatedBy, coll.lastUpdatedAt)
      }
      val shortcutCollections = welcomePageRepository.findShortcutCollections(version.id)
      val shortcutCollectionDtos = shortcutCollections.map { coll =>
        val shortcuts = welcomePageRepository.findShortcuts(coll.id)
        // Convert disabled Int to Boolean for DTO
        val disabledBoolean = coll.disabled != 0
        WelcomePageShortcutCollectionDto(coll.id, coll.title, disabledBoolean, coll.sortOrder, shortcuts, coll.lastUpdatedBy, coll.lastUpdatedAt)
      }
      WelcomePageDto(version.id, enrichedCustomization, linkCollectionDtos, shortcutCollectionDtos)
    }

    override def getPublished(handbookId: String): Option[WelcomePageDto] = inTransaction {
      welcomePageRepository.findVersion(handbookId, VersionStatus.PUBLISHED).map(getWelcomePageDto)
    }

    override def getDraft(handbookId: String): Option[WelcomePageDto] = inTransaction {
      welcomePageRepository.findVersion(handbookId, VersionStatus.DRAFT).map { draftVersion =>
        // Return draft data with its own audit fields (no overlay from published)
        getWelcomePageDto(draftVersion)
      }
    }

    override def createDraft(handbookId: String, createdBy: String): WelcomePageDto = inTransaction {
      welcomePageRepository.findVersion(handbookId, VersionStatus.DRAFT) match {
        case Some(draftVersion) => getWelcomePageDto(draftVersion)
        case None =>
          val newDraftVersion = welcomePageRepository.createVersion(handbookId, VersionStatus.DRAFT, Some(createdBy))
          welcomePageRepository.findVersion(handbookId, VersionStatus.PUBLISHED) match {
            case Some(publishedVersion) =>
              val publishedDto = getWelcomePageDto(publishedVersion)
              // Copy customization
              val newCustomization = publishedDto.customization.copy(id = "", versionId = newDraftVersion.id)
              welcomePageRepository.createCustomization(newCustomization)

              // Copy link collections (preserve lastUpdated from published)
              publishedDto.linkCollections.foreach { collDto =>
                val newCollection = welcomePageRepository.createLinkCollection(newDraftVersion.id, collDto.title, collDto.sortOrder)
                val collAuditBy = collDto.lastUpdatedBy
                val collAuditAt = collDto.lastUpdatedAt
                welcomePageRepository.updateLinkCollection(newCollection.copy(lastUpdatedBy = collAuditBy, lastUpdatedAt = collAuditAt))
                collDto.links.foreach(link =>
                  welcomePageRepository.createLink(newCollection.id, link.title, link.url, link.description, link.sortOrder)
                )
              }

              // Copy shortcut collections (preserve lastUpdated from published)
              publishedDto.shortcutCollections.foreach { collDto =>
                val newCollection = welcomePageRepository.createShortcutCollection(newDraftVersion.id, collDto.title, collDto.disabled, collDto.sortOrder)
                val collAuditBy = collDto.lastUpdatedBy
                val collAuditAt = collDto.lastUpdatedAt
                welcomePageRepository.updateShortcutCollection(newCollection.copy(lastUpdatedBy = collAuditBy, lastUpdatedAt = collAuditAt))
                collDto.shortcuts.foreach(shortcut =>
                  welcomePageRepository.createShortcut(shortcut.copy(id = "", collectionId = newCollection.id))
                )
              }
            case None =>
              // Create default customization
              val now = DateTime.now().getMillis
              val defaultCustomization = WelcomePageCustomization(
                id = "",
                versionId = newDraftVersion.id,
                primaryColor = settings.settingFor(no.kf.handboker.config.WelcomePageDefaultPrimaryColor),
                secondaryColor = settings.settingFor(no.kf.handboker.config.WelcomePageDefaultSecondaryColor),
                welcomeHeader = settings.optionalSettingFor(no.kf.handboker.config.WelcomePageDefaultHeader),
                welcomeText = settings.optionalSettingFor(no.kf.handboker.config.WelcomePageDefaultText),
                welcomeImageUrl = settings.optionalSettingFor(no.kf.handboker.config.WelcomePageDefaultImageUrl),
                imageTitle = settings.optionalSettingFor(WelcomePageDefaultImageTitle),
                altTitle = settings.optionalSettingFor(WelcomePageDefaultImageAltTitle),
                imageCredits = settings.optionalSettingFor(WelcomePageDefaultImageCredits),
                welcomeHeaderUpdatedAt = Some(now),
                welcomeTextUpdatedAt = Some(now),
                colorUpdatedAt = Some(now)
              )
              welcomePageRepository.createCustomization(defaultCustomization)
          }
          getWelcomePageDto(newDraftVersion)
      }
    }

    override def publishDraft(handbookId: String): Option[WelcomePageDto] = inTransaction {
      for {
        draftVersion <- welcomePageRepository.findVersion(handbookId, VersionStatus.DRAFT)
        publishedVersionId = welcomePageRepository.findVersion(handbookId, VersionStatus.PUBLISHED).map(_.id)
      } yield {
        welcomePageRepository.publishVersion(draftVersion.id, publishedVersionId)
        getWelcomePageDto(draftVersion.copy(status = VersionStatus.PUBLISHED))
      }
    }

    override def updateCustomization(handbookId: String, customizationDto: WelcomePageDto, lastChangedBy: String): Option[WelcomePageDto] = inTransaction {
      welcomePageRepository.findVersion(handbookId, VersionStatus.DRAFT).flatMap { draftVersion =>
        welcomePageRepository.findCustomization(draftVersion.id).map { currentCustomization =>
          val incoming = customizationDto.customization
          val now = DateTime.now().getMillis

          // Compute diffs and update audit fields server-side (ignore client-provided audit fields)
          val colorChanged = currentCustomization.primaryColor != incoming.primaryColor ||
            currentCustomization.secondaryColor != incoming.secondaryColor
          val headerChanged = currentCustomization.welcomeHeader != incoming.welcomeHeader
          val textChanged = currentCustomization.welcomeText != incoming.welcomeText
          val imageMetaChanged = currentCustomization.imageTitle != incoming.imageTitle ||
            currentCustomization.altTitle != incoming.altTitle ||
            currentCustomization.imageCredits != incoming.imageCredits

          var updated = currentCustomization.copy(
            primaryColor = incoming.primaryColor,
            secondaryColor = incoming.secondaryColor,
            // do not allow changing image file via this endpoint; keep existing URL
            welcomeImageUrl = currentCustomization.welcomeImageUrl,
            imageTitle = incoming.imageTitle,
            altTitle = incoming.altTitle,
            imageCredits = incoming.imageCredits,
            welcomeHeader = incoming.welcomeHeader,
            welcomeText = incoming.welcomeText
          )

          if (colorChanged) {
            updated = updated.copy(colorUpdatedBy = Some(lastChangedBy), colorUpdatedAt = Some(now))
          }
          if (headerChanged) {
            updated = updated.copy(welcomeHeaderUpdatedBy = Some(lastChangedBy), welcomeHeaderUpdatedAt = Some(now))
          }
          if (textChanged) {
            updated = updated.copy(welcomeTextUpdatedBy = Some(lastChangedBy), welcomeTextUpdatedAt = Some(now))
          }
          if (imageMetaChanged) {
            updated = updated.copy(imageUpdatedBy = Some(lastChangedBy), imageUpdatedAt = Some(now))
          }

          // Persist customization
          welcomePageRepository.updateCustomization(updated)

          // Fetch current collections for diffing audit
          val existingLinkCollections = welcomePageRepository.findLinkCollections(draftVersion.id)
            .map(c => c.title -> c).toMap
          val existingLinksByCollection: Map[String, Seq[WelcomePageLink]] = existingLinkCollections.values.map { c =>
            c.title -> welcomePageRepository.findLinks(c.id)
          }.toMap

          val existingShortcutCollections = welcomePageRepository.findShortcutCollections(draftVersion.id)
            .map(c => c.title -> c).toMap
          val existingShortcutsByCollection: Map[String, Seq[WelcomePageShortcut]] = existingShortcutCollections.values.map { c =>
            c.title -> welcomePageRepository.findShortcuts(c.id)
          }.toMap

          // Update link collections
          welcomePageRepository.deleteLinkCollections(draftVersion.id)
          customizationDto.linkCollections.foreach { collDto =>
            val newCollection = welcomePageRepository.createLinkCollection(draftVersion.id, collDto.title, collDto.sortOrder)
            collDto.links.foreach { link =>
              welcomePageRepository.createLink(newCollection.id, link.title, link.url, link.description, link.sortOrder)
            }
            // Determine audit
            val maybeOld = existingLinkCollections.get(collDto.title)
            val oldLinks = existingLinksByCollection.getOrElse(collDto.title, Seq.empty)
            val changed = maybeOld.isEmpty || maybeOld.exists(o => o.sortOrder != collDto.sortOrder) || {
              val oldNorm = oldLinks.map(l => (l.title, l.url, l.description.getOrElse(null), l.sortOrder))
              val newNorm = collDto.links.map(l => (l.title, l.url, l.description.getOrElse(null), l.sortOrder))
              oldNorm != newNorm
            }
            val collAuditBy = if (changed) Some(lastChangedBy) else maybeOld.flatMap(_.lastUpdatedBy)
            val collAuditAt = if (changed) Some(now) else maybeOld.flatMap(_.lastUpdatedAt)
            welcomePageRepository.updateLinkCollection(newCollection.copy(lastUpdatedBy = collAuditBy, lastUpdatedAt = collAuditAt))
          }

          // Update shortcut collections
          welcomePageRepository.deleteShortcutCollections(draftVersion.id)
          customizationDto.shortcutCollections.foreach { collDto =>
            val newCollection = welcomePageRepository.createShortcutCollection(draftVersion.id, collDto.title, collDto.disabled, collDto.sortOrder)
            collDto.shortcuts.foreach { shortcut =>
              welcomePageRepository.createShortcut(shortcut.copy(id = "", collectionId = newCollection.id))
            }
            // Determine audit
            val maybeOld = existingShortcutCollections.get(collDto.title)
            val oldShortcuts = existingShortcutsByCollection.getOrElse(collDto.title, Seq.empty)
            val changed = maybeOld.isEmpty || maybeOld.exists(o => o.sortOrder != collDto.sortOrder) || {
              val oldNorm = oldShortcuts.map(s => (s.title, s.description.getOrElse(null), s.sortOrder, s.link.getOrElse(null), s.handbookId.getOrElse(null), s.sectionId.getOrElse(null), s.chapterId.getOrElse(null)))
              val newNorm = collDto.shortcuts.map(s => (s.title, s.description.getOrElse(null), s.sortOrder, s.link.getOrElse(null), s.handbookId.getOrElse(null), s.sectionId.getOrElse(null), s.chapterId.getOrElse(null)))
              oldNorm != newNorm
            }
            val collAuditBy = if (changed) Some(lastChangedBy) else maybeOld.flatMap(_.lastUpdatedBy)
            val collAuditAt = if (changed) Some(now) else maybeOld.flatMap(_.lastUpdatedAt)
            welcomePageRepository.updateShortcutCollection(newCollection.copy(lastUpdatedBy = collAuditBy, lastUpdatedAt = collAuditAt))
          }

          getWelcomePageDto(draftVersion)
        }
      }
    }

    override def discardDraft(handbookId: String): Boolean = inTransaction {
      welcomePageRepository.findVersion(handbookId, VersionStatus.DRAFT) match {
        case Some(draftVersion) =>
          // Clean up any associated image before deleting the draft
          val customization = welcomePageRepository.findCustomization(draftVersion.id)
          customization.flatMap(_.welcomeImageUrl).foreach { imagePath =>
            // Extract filename from path (e.g., "/handboker/images/filename.jpg" -> "filename.jpg")
            val filename = imagePath.split("/").last
            imageService.deleteImage(filename)
          }
          welcomePageRepository.deleteVersion(draftVersion.id)
          true
        case None =>
          false // No draft exists to discard
      }
    }

    override def updateWelcomeImage(handbookId: String, image: Image, updatedBy: String): Option[WelcomePageDto] = inTransaction {
      welcomePageRepository.findVersion(handbookId, VersionStatus.DRAFT).flatMap { draftVersion =>
        welcomePageRepository.findCustomization(draftVersion.id).map { customization =>
          // Delete old image if it exists
          customization.welcomeImageUrl.foreach { oldImagePath =>
            val oldFilename = oldImagePath.split("/").last
            imageService.deleteImage(oldFilename)
          }

          // Persist new image with deterministic cover naming
          val imageUrl = imageService.persistCoverImage(handbookId, draftVersion.id, image.fileExtension, image.file)
          val now = DateTime.now().getMillis

          // Update customization with new image
          val updatedCustomization = customization.copy(
            welcomeImageUrl = Some(imageUrl),
            imageUpdatedBy = Some(updatedBy),
            imageUpdatedAt = Some(now)
          )

          welcomePageRepository.updateCustomization(updatedCustomization)
          getWelcomePageDto(draftVersion)
        }
      }
    }

    override def removeWelcomeImage(handbookId: String, updatedBy: String): Option[WelcomePageDto] = inTransaction {
      welcomePageRepository.findVersion(handbookId, VersionStatus.DRAFT).flatMap { draftVersion =>
        welcomePageRepository.findCustomization(draftVersion.id).map { customization =>
          // Delete existing image if it exists
          customization.welcomeImageUrl.foreach { imagePath =>
            val filename = imagePath.split("/").last
            imageService.deleteImage(filename)
          }

          val now = DateTime.now().getMillis

          // Update customization to remove image
          val updatedCustomization = customization.copy(
            welcomeImageUrl = None,
            imageTitle = None,
            altTitle = None,
            imageCredits = None,
            imageUpdatedBy = Some(updatedBy),
            imageUpdatedAt = Some(now)
          )

          welcomePageRepository.updateCustomization(updatedCustomization)
          getWelcomePageDto(draftVersion)
        }
      }
    }

    override def updateCustomizationWithImage(handbookId: String, customizationDto: WelcomePageDto, image: Option[Image], lastChangedBy: String): Option[WelcomePageDto] = inTransaction {
      welcomePageRepository.findVersion(handbookId, VersionStatus.DRAFT).flatMap { draftVersion =>
        welcomePageRepository.findCustomization(draftVersion.id).map { currentCustomization =>
          val incoming = customizationDto.customization
          val now = DateTime.now().getMillis

          // Start from current state and apply incoming values (ignore client-sent audit fields)
          var updated = currentCustomization.copy(
            primaryColor = incoming.primaryColor,
            secondaryColor = incoming.secondaryColor,
            welcomeHeader = incoming.welcomeHeader,
            welcomeText = incoming.welcomeText,
            imageTitle = incoming.imageTitle,
            altTitle = incoming.altTitle,
            imageCredits = incoming.imageCredits
          )

          // Handle image file update if provided
          image.foreach { newImage =>
            // Delete old image if it exists
            currentCustomization.welcomeImageUrl.foreach { oldImagePath =>
              val oldFilename = oldImagePath.split("/").last
              imageService.deleteImage(oldFilename)
            }
            val imageUrl = imageService.persistCoverImage(handbookId, draftVersion.id, newImage.fileExtension, newImage.file)
            updated = updated.copy(
              welcomeImageUrl = Some(imageUrl),
              imageUpdatedBy = Some(lastChangedBy),
              imageUpdatedAt = Some(now)
            )
          }

          // If no new image file, keep existing URL
          if (image.isEmpty) {
            updated = updated.copy(welcomeImageUrl = currentCustomization.welcomeImageUrl)
          }

          // Compute diffs for audit fields
          val colorChanged = currentCustomization.primaryColor != incoming.primaryColor ||
            currentCustomization.secondaryColor != incoming.secondaryColor
          val headerChanged = currentCustomization.welcomeHeader != incoming.welcomeHeader
          val textChanged = currentCustomization.welcomeText != incoming.welcomeText
          val imageMetaChanged = currentCustomization.imageTitle != incoming.imageTitle ||
            currentCustomization.altTitle != incoming.altTitle ||
            currentCustomization.imageCredits != incoming.imageCredits

          if (colorChanged) {
            updated = updated.copy(colorUpdatedBy = Some(lastChangedBy), colorUpdatedAt = Some(now))
          }
          if (headerChanged) {
            updated = updated.copy(welcomeHeaderUpdatedBy = Some(lastChangedBy), welcomeHeaderUpdatedAt = Some(now))
          }
          if (textChanged) {
            updated = updated.copy(welcomeTextUpdatedBy = Some(lastChangedBy), welcomeTextUpdatedAt = Some(now))
          }
          // Update image audit fields if metadata changed and no new image file was uploaded
          if (image.isEmpty && imageMetaChanged) {
            updated = updated.copy(imageUpdatedBy = Some(lastChangedBy), imageUpdatedAt = Some(now))
          }

          // Persist customization
          welcomePageRepository.updateCustomization(updated)

          // Update link collections
          welcomePageRepository.deleteLinkCollections(draftVersion.id)
          customizationDto.linkCollections.foreach { collDto =>
            val newCollection = welcomePageRepository.createLinkCollection(draftVersion.id, collDto.title, collDto.sortOrder)
            collDto.links.foreach { link =>
              welcomePageRepository.createLink(newCollection.id, link.title, link.url, link.description, link.sortOrder)
            }
          }

          // Update shortcut collections
          welcomePageRepository.deleteShortcutCollections(draftVersion.id)
          customizationDto.shortcutCollections.foreach { collDto =>
            val newCollection = welcomePageRepository.createShortcutCollection(draftVersion.id, collDto.title, collDto.disabled, collDto.sortOrder)
            collDto.shortcuts.foreach { shortcut =>
              welcomePageRepository.createShortcut(shortcut.copy(id = "", collectionId = newCollection.id))
            }
          }

          getWelcomePageDto(draftVersion)
        }
      }
    }
  }
}

trait WelcomePageService {
  def getPublished(handbookId: String): Option[WelcomePageDto]
  def getDraft(handbookId: String): Option[WelcomePageDto]
  def createDraft(handbookId: String, createdBy: String): WelcomePageDto
  def publishDraft(handbookId: String): Option[WelcomePageDto]
  def updateCustomization(handbookId: String, customization: WelcomePageDto, lastChangedBy: String): Option[WelcomePageDto]
  def discardDraft(handbookId: String): Boolean
  def updateWelcomeImage(handbookId: String, image: Image, updatedBy: String): Option[WelcomePageDto]
  def removeWelcomeImage(handbookId: String, updatedBy: String): Option[WelcomePageDto]
  def updateCustomizationWithImage(handbookId: String, customization: WelcomePageDto, image: Option[Image], lastChangedBy: String): Option[WelcomePageDto]
}