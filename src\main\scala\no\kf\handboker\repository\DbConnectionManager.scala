package no.kf.handboker.repository

import no.kf.handboker.config._
import no.kf.util.Logging
import no.kf.util.ResourceUtils
import no.kf.util.JavaUtils
import no.kf.db.DbConnectionValues
import no.kf.db.DbConnectionManager

trait DbConnectionManagerComponent {

  this: AppSettingComponent =>

  implicit val connectionManager: DbConnectionManager


  class DbConnectionManagerImpl extends DbConnectionManager with Logging with ResourceUtils {
    import JavaUtils._

    lazy val databaseHost: String = settings.settingFor(DatabaseHost)
    lazy val databaseName: String = settings.settingFor(DatabaseName)
    lazy val persistenceStore: String = settings.settingFor(PersistenceStore)
    override lazy val runMigrationScripts: Boolean = settings.settingFor(RunMigrationScripts).toBoolean
    override lazy val connValues = settings.settingFor(Database) match {
      case "MYSQL" => DbConnectionValues.getMySqlValues(databaseHost, databaseName)
      case "MSSQL" => DbConnectionValues.getMsSqlValues(databaseHost, databaseName)
      case "DERBY" => DbConnectionValues.getDerbyValues(persistenceStore, databaseName)
      case "DERBYMEM" => DbConnectionValues.getDerbyInMemoryValues(databaseName)
    }
    lazy val databaseUsername: String = settings.settingFor(DatabaseUsername)
    lazy val databasePassword: String = settings.settingFor(DatabasePassword)

    lazy val scriptName: String = "create-db.sql"
    lazy val migrateScriptPrefixName: String = "migrate-db-"
    lazy val currentVersionNumber: Int = 27
    lazy val minMigrationVersion: Int = 2
    lazy val metaDataTable = "handboker_meta_data"

    override lazy val dummyDataScriptName = ?(settings.value(MockDataScript).getOrElse(""))

  }

}
