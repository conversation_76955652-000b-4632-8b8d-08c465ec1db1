package no.kf.handboker.service

import better.files._
import no.kf.db.testing.TransactionManagerMock
import no.kf.handboker.{ComponentRegistry, DefaultTestDI}
import no.kf.handboker.model.Image
import no.kf.handboker.model.welcomepage._
import no.kf.handboker.repository.welcomepage.WelcomePageRepository
import no.kf.handboker.service.welcomepage.WelcomePageService
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.mockito.Mockito._
import org.mockito.Matchers._
import org.mockito.ArgumentCaptor
import org.scalatest.{BeforeAndAfterEach, FunSuite}
import org.scalatest.junit.JUnitRunner
import org.scalatest.mockito.MockitoSugar

@RunWith(classOf[JUnitRunner])
class WelcomePageServiceTest extends FunSuite with DefaultTestDI with MockitoSugar with BeforeAndAfterEach {

  override val componentRegistry: ComponentRegistry = new DefaultTestRegistry with TransactionManagerMock {
    override lazy val welcomePageService: WelcomePageService = new WelcomePageServiceImpl
    override lazy val welcomePageRepository: WelcomePageRepository = mock[WelcomePageRepository]
    override lazy val imageService: ImageService = mock[ImageService]
  }

  private val service = componentRegistry.welcomePageService
  private val repo = componentRegistry.welcomePageRepository
  private val imageSvc = componentRegistry.imageService

  private val handbookId = "hb-1"
  private val draftVersion = WelcomePageVersion("v-draft", handbookId, VersionStatus.DRAFT, DateTime.now().getMillis, DateTime.now().getMillis, Some("user@test"))
  private val publishedVersion = WelcomePageVersion("v-pub", handbookId, VersionStatus.PUBLISHED, DateTime.now().minusDays(1).getMillis, DateTime.now().minusDays(1).getMillis, Some("user@test"))

  override def beforeEach(): Unit = {
    reset(repo, imageSvc)
  }

  test("getDraft populates welcomeImageResponse when welcomeImageUrl exists") {
    // Arrange
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))
    // Ensure overlay lookup does not NPE
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(None)

    val imgFileName = "abc123.jpg"
    val imageUrl = s"/handboker/images/$imgFileName"

    val customization = WelcomePageCustomization(
      id = "c1", versionId = draftVersion.id, primaryColor = "#111111", secondaryColor = "#222222",
      welcomeImageUrl = Some(imageUrl)
    )

    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(customization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Build a temp file to return from image service
    val tmp = File.newTemporaryFile(suffix = ".jpg").overwrite("test-bytes")
    when(imageSvc.retrieveImage(imgFileName)).thenReturn(tmp)

    // Act
    val result = service.getDraft(handbookId)

    // Assert
    assert(result.isDefined)
    val cust = result.get.customization
    assert(cust.welcomeImageUrl.contains(imageUrl))
    assert(cust.welcomeImageResponse.isDefined)
    assert(cust.welcomeImageResponse.get.startsWith("data:image/"))
  }

  test("getDraft shows draft's own audit fields") {
    // Arrange: Draft exists with its own audit fields
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val draftCust = WelcomePageCustomization("c-d", draftVersion.id, "#111111", "#222222",
      colorUpdatedBy = Some("draft-user"), colorUpdatedAt = Some(100L),
      imageUpdatedBy = Some("draft-image-user"), imageUpdatedAt = Some(200L),
      welcomeHeaderUpdatedBy = Some("draft-header-user"), welcomeHeaderUpdatedAt = Some(300L),
      welcomeTextUpdatedBy = Some("draft-text-user"), welcomeTextUpdatedAt = Some(400L))

    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(draftCust))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act
    val result = service.getDraft(handbookId)

    // Assert: Draft shows its own audit fields
    val cust = result.get.customization
    assert(cust.colorUpdatedBy.contains("draft-user"))
    assert(cust.colorUpdatedAt.contains(100L))
    assert(cust.imageUpdatedBy.contains("draft-image-user"))
    assert(cust.imageUpdatedAt.contains(200L))
    assert(cust.welcomeHeaderUpdatedBy.contains("draft-header-user"))
    assert(cust.welcomeHeaderUpdatedAt.contains(300L))
    assert(cust.welcomeTextUpdatedBy.contains("draft-text-user"))
    assert(cust.welcomeTextUpdatedAt.contains(400L))
  }

  test("updateWelcomeImage persists new image with deterministic name and deletes old if present") {
    // Arrange
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val oldFileName = "old999.png"
    val oldUrl = s"/handboker/images/$oldFileName"

    val currentCustomization = WelcomePageCustomization(
      id = "c1", versionId = draftVersion.id, primaryColor = "#111111", secondaryColor = "#222222",
      welcomeImageUrl = Some(oldUrl)
    )

    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(currentCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    val newImage = Image("jpg", "new image bytes".getBytes("UTF-8"))
    val expectedFileName = s"cover_${handbookId}_${draftVersion.id}_image.${newImage.fileExtension}"
    val newUrl = s"/handboker/images/cover_image/$expectedFileName"

    when(imageSvc.persistCoverImage(handbookId, draftVersion.id, newImage.fileExtension, newImage.file)).thenReturn(newUrl)
    when(repo.updateCustomization(any[WelcomePageCustomization])).thenAnswer(invocation => invocation.getArguments.apply(0).asInstanceOf[WelcomePageCustomization])

    // Compose response dependencies for getWelcomePageDto (second call to findCustomization)
    val updatedCustomization = currentCustomization.copy(welcomeImageUrl = Some(newUrl))
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(currentCustomization), Some(updatedCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Image retrieval for enriched response
    val tmpNew = File.newTemporaryFile(suffix = ".jpg").overwrite("new-bytes")
    when(imageSvc.retrieveImage(expectedFileName)).thenReturn(tmpNew)

    // Act
    val result = service.updateWelcomeImage(handbookId, newImage, updatedBy = "tester@test")

    // Assert
    assert(result.isDefined)
    val cust = result.get.customization
    assert(cust.welcomeImageUrl.contains(newUrl))
    verify(imageSvc, times(1)).deleteImage(oldFileName)
  }

  test("removeWelcomeImage clears URL and deletes file") {
    // Arrange
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val oldFileName = "to-remove.png"
    val oldUrl = s"/handboker/images/$oldFileName"

    val currentCustomization = WelcomePageCustomization(
      id = "c1", versionId = draftVersion.id, primaryColor = "#111111", secondaryColor = "#222222",
      welcomeImageUrl = Some(oldUrl)
    )

    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(currentCustomization))
    when(repo.updateCustomization(any[WelcomePageCustomization])).thenAnswer(invocation => invocation.getArguments.apply(0).asInstanceOf[WelcomePageCustomization])
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(currentCustomization), Some(currentCustomization.copy(welcomeImageUrl = None)))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act
    val result = service.removeWelcomeImage(handbookId, updatedBy = "tester@test")

    // Assert
    assert(result.isDefined)
    val cust = result.get.customization
    assert(cust.welcomeImageUrl.isEmpty)
    verify(imageSvc, times(1)).deleteImage(oldFileName)
  }

  test("updateCustomization replaces link and shortcut collections") {
    // Arrange
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val dto = WelcomePageDto(
      versionId = draftVersion.id,
      customization = WelcomePageCustomization("c1", draftVersion.id, "#000000", "#ffffff"),
      linkCollections = Seq(WelcomePageLinkCollectionDto("lc1", "Links", 1, Seq(
        WelcomePageLink("l1", "lc1", "A", "https://a", None, 1)
      ))),
      shortcutCollections = Seq(WelcomePageShortcutCollectionDto("sc1", "Shortcuts", disabled = false, 1, Seq(
        WelcomePageShortcut("s1", "sc1", "S", None, 1, None, None, None, None)
      )))
    )

    when(repo.updateCustomization(dto.customization)).thenReturn(dto.customization)
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(dto.customization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    when(repo.createLinkCollection(draftVersion.id, "Links", 1))
      .thenReturn(WelcomePageLinkCollection("newLC", draftVersion.id, "Links", 1))

    when(repo.createShortcutCollection(draftVersion.id, "Shortcuts", disabled = false, 1))
      .thenReturn(WelcomePageShortcutCollection("newSC", draftVersion.id, "Shortcuts", disabled = 0, 1))

    // Act
    val result = service.updateCustomization(handbookId, dto, lastChangedBy = "tester@test")

    // Assert
    assert(result.isDefined)
    verify(repo, times(1)).deleteLinkCollections(draftVersion.id)
    verify(repo, times(1)).deleteShortcutCollections(draftVersion.id)
    verify(repo, times(1)).createLinkCollection(draftVersion.id, "Links", 1)
    verify(repo, times(1)).createLink("newLC", "A", "https://a", None, 1)
    val scCaptor = ArgumentCaptor.forClass(classOf[WelcomePageShortcut])
    verify(repo, times(1)).createShortcut(scCaptor.capture())
    assert(scCaptor.getValue.collectionId == "newSC")
  }

  test("publishDraft promotes draft to published") {
    // Arrange
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(publishedVersion))

    val customization = WelcomePageCustomization("c1", draftVersion.id, "#111111", "#222222")
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(customization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act
    val result = service.publishDraft(handbookId)

    // Assert
    assert(result.isDefined)
    verify(repo, times(1)).publishVersion(draftVersion.id, Some(publishedVersion.id))
  }


  test("updateCustomization computes color audit and ignores client-provided audit fields") {
    // Arrange
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val current = WelcomePageCustomization(
      id = "c1", versionId = draftVersion.id, primaryColor = "#111111", secondaryColor = "#222222",
      welcomeHeader = Some("Header"), welcomeText = Some("Text"), welcomeImageUrl = Some("/handboker/images/cover_image/cover_hb_v_image.jpg")
    )

    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(current))
    when(repo.updateCustomization(org.mockito.Matchers.any[WelcomePageCustomization])).thenAnswer(invocation => invocation.getArguments.apply(0).asInstanceOf[WelcomePageCustomization])
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    val incoming = current.copy(
      primaryColor = "#333333", // change color triggers color audit
      colorUpdatedBy = Some("hacker@client"), // should be ignored
      colorUpdatedAt = Some(999L),
      welcomeImageUrl = Some("/evil/change.jpg") // should be ignored by service
    )
    val dto = WelcomePageDto(draftVersion.id, incoming, Nil, Nil)

    // Act
    val result = service.updateCustomization(handbookId, dto, lastChangedBy = "tester@test")

    // Assert
    assert(result.isDefined)
    val captor = ArgumentCaptor.forClass(classOf[WelcomePageCustomization])
    verify(repo).updateCustomization(captor.capture())
    val updated = captor.getValue
    assert(updated.primaryColor == "#333333")
    assert(updated.colorUpdatedBy.contains("tester@test"))
    assert(updated.colorUpdatedAt.isDefined)
    // ensure client value not applied and URL preserved
    assert(updated.welcomeImageUrl.contains("/handboker/images/cover_image/cover_hb_v_image.jpg"))
  }

  test("updateCustomizationWithImage updates image audit for metadata-only changes and keeps URL when no file uploaded") {
    // Arrange
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val current = WelcomePageCustomization(
      id = "c1", versionId = draftVersion.id, primaryColor = "#111111", secondaryColor = "#222222",
      welcomeImageUrl = None, imageTitle = Some("Old"), altTitle = None, imageCredits = None
    )

    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(current))
    when(repo.updateCustomization(org.mockito.Matchers.any[WelcomePageCustomization])).thenAnswer(invocation => invocation.getArguments.apply(0).asInstanceOf[WelcomePageCustomization])
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    val incoming = current.copy(imageTitle = Some("New Title")) // metadata change only
    val dto = WelcomePageDto(draftVersion.id, incoming, Nil, Nil)

    // Act
    val result = service.updateCustomizationWithImage(handbookId, dto, image = None, lastChangedBy = "tester@test")

    // Assert
    assert(result.isDefined)
    val captor = ArgumentCaptor.forClass(classOf[WelcomePageCustomization])
    verify(repo).updateCustomization(captor.capture())
    val updated = captor.getValue
    assert(updated.imageTitle.contains("New Title"))
    assert(updated.imageUpdatedBy.contains("tester@test"))
    assert(updated.imageUpdatedAt.isDefined)
    assert(updated.welcomeImageUrl.isEmpty)
  }

  test("link collection audit: content change then sort change preserves most recent audit") {
    // Scenario: kushan updates link content (2025-03-10), tharindu sorts links (2025-04-20)
    // Expected: final audit shows tharindu, 2025-04-20

    val kushanTime = new DateTime(2025, 3, 10, 10, 0).getMillis
    val tharinduTime = new DateTime(2025, 4, 20, 14, 30).getMillis

    // Setup: published version with kushan's content change
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(publishedVersion))
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val publishedCustomization = WelcomePageCustomization("c-pub", publishedVersion.id, "#000000", "#ffffff")
    when(repo.findCustomization(publishedVersion.id)).thenReturn(Some(publishedCustomization))
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(publishedCustomization.copy(id = "c-draft", versionId = draftVersion.id)))

    // Published state: link collection with kushan's audit from content change
    val publishedLinkColl = WelcomePageLinkCollection("lc-pub", publishedVersion.id, "External Links", 1, Some("kushan"), Some(kushanTime))
    when(repo.findLinkCollections(publishedVersion.id)).thenReturn(List(publishedLinkColl))
    when(repo.findLinks("lc-pub")).thenReturn(List(
      WelcomePageLink("l1", "lc-pub", "Link A", "https://a.com", None, 1),
      WelcomePageLink("l2", "lc-pub", "Link B", "https://b.com", None, 2)
    ))
    when(repo.findShortcutCollections(publishedVersion.id)).thenReturn(Nil)

    // Draft state: same collection structure initially (copied from published)
    val draftLinkColl = WelcomePageLinkCollection("lc-draft", draftVersion.id, "External Links", 1, Some("kushan"), Some(kushanTime))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(List(draftLinkColl))
    when(repo.findLinks("lc-draft")).thenReturn(List(
      WelcomePageLink("l1-draft", "lc-draft", "Link A", "https://a.com", None, 1),
      WelcomePageLink("l2-draft", "lc-draft", "Link B", "https://b.com", None, 2)
    ))
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Mock repository operations for the update
    when(repo.createLinkCollection(draftVersion.id, "External Links", 1))
      .thenReturn(WelcomePageLinkCollection("lc-new", draftVersion.id, "External Links", 1))
    when(repo.updateLinkCollection(any[WelcomePageLinkCollection])).thenAnswer(invocation =>
      invocation.getArguments.apply(0).asInstanceOf[WelcomePageLinkCollection])

    // Tharindu sorts the links (Link B now comes first)
    val sortedDto = WelcomePageDto(
      versionId = draftVersion.id,
      customization = publishedCustomization.copy(id = "c-draft", versionId = draftVersion.id),
      linkCollections = Seq(WelcomePageLinkCollectionDto("lc-draft", "External Links", 1, Seq(
        WelcomePageLink("l2-draft", "lc-draft", "Link B", "https://b.com", None, 1), // moved to position 1
        WelcomePageLink("l1-draft", "lc-draft", "Link A", "https://a.com", None, 2) // moved to position 2
      ))),
      shortcutCollections = Seq.empty
    )

    // Act: tharindu updates (sorts) the links
    val result = service.updateCustomization(handbookId, sortedDto, "tharindu")

    // Assert: verify that updateLinkCollection was called with tharindu's audit
    val collectionCaptor = ArgumentCaptor.forClass(classOf[WelcomePageLinkCollection])
    verify(repo, times(1)).updateLinkCollection(collectionCaptor.capture())

    val updatedCollection = collectionCaptor.getValue
    assert(updatedCollection.lastUpdatedBy.contains("tharindu"))
    assert(updatedCollection.lastUpdatedAt.exists(_ >= tharinduTime))
    assert(result.isDefined)
  }

  test("shortcut collection audit: content change then sort change preserves most recent audit") {
    // Scenario: kushan updates shortcut content (2025-03-10), tharindu sorts shortcuts (2025-04-20)
    // Expected: final audit shows tharindu, 2025-04-20

    val kushanTime = new DateTime(2025, 3, 10, 10, 0).getMillis
    val tharinduTime = new DateTime(2025, 4, 20, 14, 30).getMillis

    // Setup: published version with kushan's content change
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(publishedVersion))
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val publishedCustomization = WelcomePageCustomization("c-pub", publishedVersion.id, "#000000", "#ffffff")
    when(repo.findCustomization(publishedVersion.id)).thenReturn(Some(publishedCustomization))
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(publishedCustomization.copy(id = "c-draft", versionId = draftVersion.id)))

    // Published state: shortcut collection with kushan's audit from content change
    val publishedShortcutColl = WelcomePageShortcutCollection("sc-pub", publishedVersion.id, "Quick Access", 0, 1, Some("kushan"), Some(kushanTime))
    when(repo.findShortcutCollections(publishedVersion.id)).thenReturn(List(publishedShortcutColl))
    when(repo.findShortcuts("sc-pub")).thenReturn(List(
      WelcomePageShortcut("s1", "sc-pub", "Shortcut A", Some("Description A"), 1, None, Some("hb1"), None, None),
      WelcomePageShortcut("s2", "sc-pub", "Shortcut B", Some("Description B"), 2, None, Some("hb2"), None, None)
    ))
    when(repo.findLinkCollections(publishedVersion.id)).thenReturn(Nil)

    // Draft state: same collection structure initially (copied from published)
    val draftShortcutColl = WelcomePageShortcutCollection("sc-draft", draftVersion.id, "Quick Access", 0, 1, Some("kushan"), Some(kushanTime))
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(List(draftShortcutColl))
    when(repo.findShortcuts("sc-draft")).thenReturn(List(
      WelcomePageShortcut("s1-draft", "sc-draft", "Shortcut A", Some("Description A"), 1, None, Some("hb1"), None, None),
      WelcomePageShortcut("s2-draft", "sc-draft", "Shortcut B", Some("Description B"), 2, None, Some("hb2"), None, None)
    ))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)

    // Mock repository operations for the update
    when(repo.createShortcutCollection(draftVersion.id, "Quick Access", false, 1))
      .thenReturn(WelcomePageShortcutCollection("sc-new", draftVersion.id, "Quick Access", 0, 1))
    when(repo.updateShortcutCollection(any[WelcomePageShortcutCollection])).thenAnswer(invocation =>
      invocation.getArguments.apply(0).asInstanceOf[WelcomePageShortcutCollection])

    // Tharindu sorts the shortcuts (Shortcut B now comes first)
    val sortedDto = WelcomePageDto(
      versionId = draftVersion.id,
      customization = publishedCustomization.copy(id = "c-draft", versionId = draftVersion.id),
      linkCollections = Seq.empty,
      shortcutCollections = Seq(WelcomePageShortcutCollectionDto("sc-draft", "Quick Access", disabled = false, 1, Seq(
        WelcomePageShortcut("s2-draft", "sc-draft", "Shortcut B", Some("Description B"), 1, None, Some("hb2"), None, None), // moved to position 1
        WelcomePageShortcut("s1-draft", "sc-draft", "Shortcut A", Some("Description A"), 2, None, Some("hb1"), None, None) // moved to position 2
      )))
    )

    // Act: tharindu updates (sorts) the shortcuts
    val result = service.updateCustomization(handbookId, sortedDto, "tharindu")

    // Assert: verify that updateShortcutCollection was called with tharindu's audit
    val collectionCaptor = ArgumentCaptor.forClass(classOf[WelcomePageShortcutCollection])
    verify(repo, times(1)).updateShortcutCollection(collectionCaptor.capture())

    val updatedCollection = collectionCaptor.getValue
    assert(updatedCollection.lastUpdatedBy.contains("tharindu"))
    assert(updatedCollection.lastUpdatedAt.exists(_ >= tharinduTime))
    assert(result.isDefined)
  }

  test("createDraft preserves published collection audit") {
    // Scenario: published version has collections with audit, createDraft should preserve those audit values

    val originalTime = new DateTime(2025, 3, 10, 10, 0).getMillis

    // Setup: no existing draft
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(None)
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(publishedVersion))

    val newDraftVersion = WelcomePageVersion("v-new-draft", handbookId, VersionStatus.DRAFT, DateTime.now().getMillis, DateTime.now().getMillis, Some("creator"))
    when(repo.createVersion(handbookId, VersionStatus.DRAFT, Some("creator"))).thenReturn(newDraftVersion)

    // Published customization and collections with audit
    val publishedCustomization = WelcomePageCustomization("c-pub", publishedVersion.id, "#000000", "#ffffff")
    when(repo.findCustomization(publishedVersion.id)).thenReturn(Some(publishedCustomization))

    val publishedLinkColl = WelcomePageLinkCollection("lc-pub", publishedVersion.id, "External Links", 1, Some("kushan"), Some(originalTime))
    val publishedShortcutColl = WelcomePageShortcutCollection("sc-pub", publishedVersion.id, "Quick Access", 0, 1, Some("tharindu"), Some(originalTime))

    when(repo.findLinkCollections(publishedVersion.id)).thenReturn(List(publishedLinkColl))
    when(repo.findLinks("lc-pub")).thenReturn(List(WelcomePageLink("l1", "lc-pub", "Link A", "https://a.com", None, 1)))
    when(repo.findShortcutCollections(publishedVersion.id)).thenReturn(List(publishedShortcutColl))
    when(repo.findShortcuts("sc-pub")).thenReturn(List(WelcomePageShortcut("s1", "sc-pub", "Shortcut A", None, 1, None, Some("hb1"), None, None)))

    // Mock the creation and update calls
    val newLinkColl = WelcomePageLinkCollection("lc-new", newDraftVersion.id, "External Links", 1)
    val newShortcutColl = WelcomePageShortcutCollection("sc-new", newDraftVersion.id, "Quick Access", 0, 1)

    when(repo.createLinkCollection(newDraftVersion.id, "External Links", 1)).thenReturn(newLinkColl)
    when(repo.createShortcutCollection(newDraftVersion.id, "Quick Access", false, 1)).thenReturn(newShortcutColl)
    when(repo.updateLinkCollection(any[WelcomePageLinkCollection])).thenAnswer(invocation => invocation.getArguments.apply(0).asInstanceOf[WelcomePageLinkCollection])
    when(repo.updateShortcutCollection(any[WelcomePageShortcutCollection])).thenAnswer(invocation => invocation.getArguments.apply(0).asInstanceOf[WelcomePageShortcutCollection])
    when(repo.createCustomization(any[WelcomePageCustomization])).thenAnswer(invocation => invocation.getArguments.apply(0).asInstanceOf[WelcomePageCustomization])

    // Mock the final getWelcomePageDto call
    when(repo.findCustomization(newDraftVersion.id)).thenReturn(Some(publishedCustomization.copy(id = "c-new", versionId = newDraftVersion.id)))
    when(repo.findLinkCollections(newDraftVersion.id)).thenReturn(List(newLinkColl.copy(lastUpdatedBy = Some("kushan"), lastUpdatedAt = Some(originalTime))))
    when(repo.findLinks("lc-new")).thenReturn(List(WelcomePageLink("l1-new", "lc-new", "Link A", "https://a.com", None, 1)))
    when(repo.findShortcutCollections(newDraftVersion.id)).thenReturn(List(newShortcutColl.copy(lastUpdatedBy = Some("tharindu"), lastUpdatedAt = Some(originalTime))))
    when(repo.findShortcuts("sc-new")).thenReturn(List(WelcomePageShortcut("s1-new", "sc-new", "Shortcut A", None, 1, None, Some("hb1"), None, None)))

    // Act
    val result = service.createDraft(handbookId, "creator")

    // Assert: verify that collections were updated with preserved audit
    val linkCollCaptor = ArgumentCaptor.forClass(classOf[WelcomePageLinkCollection])
    val shortcutCollCaptor = ArgumentCaptor.forClass(classOf[WelcomePageShortcutCollection])

    verify(repo, times(1)).updateLinkCollection(linkCollCaptor.capture())
    verify(repo, times(1)).updateShortcutCollection(shortcutCollCaptor.capture())

    val updatedLinkColl = linkCollCaptor.getValue
    val updatedShortcutColl = shortcutCollCaptor.getValue

    assert(updatedLinkColl.lastUpdatedBy.contains("kushan"))
    assert(updatedLinkColl.lastUpdatedAt.contains(originalTime))
    assert(updatedShortcutColl.lastUpdatedBy.contains("tharindu"))
    assert(updatedShortcutColl.lastUpdatedAt.contains(originalTime))

    // Verify the final DTO includes the preserved audit
    assert(result.linkCollections.head.lastUpdatedBy.contains("kushan"))
    assert(result.linkCollections.head.lastUpdatedAt.contains(originalTime))
    assert(result.shortcutCollections.head.lastUpdatedBy.contains("tharindu"))
    assert(result.shortcutCollections.head.lastUpdatedAt.contains(originalTime))
  }

  test("getDraft shows draft audit: color updated by draft editor") {
    // Scenario: Draft editor changes primary color in draft
    // Draft page should show: Last updated by: draft-editor | current time

    val draftTime = new DateTime(2025, 3, 15, 16, 30).getMillis

    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    // Draft customization with its own audit
    val draftCustomization = WelcomePageCustomization("c-draft", draftVersion.id, "#111111", "#222222",
      colorUpdatedBy = Some("draft-editor"), colorUpdatedAt = Some(draftTime))
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(draftCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act
    val result = service.getDraft(handbookId)

    // Assert: draft should show its own audit
    assert(result.isDefined)
    val dto = result.get
    assert(dto.customization.colorUpdatedBy.contains("draft-editor"))
    assert(dto.customization.colorUpdatedAt.contains(draftTime))
  }

  test("getDraft shows draft audit: image updated by draft editor") {
    // Scenario: Draft editor changes welcome image in draft
    // Draft page should show: Last updated by: draft-editor | current time

    val draftTime = new DateTime(2025, 3, 16, 11, 45).getMillis

    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    // Draft customization with its own image audit
    val draftCustomization = WelcomePageCustomization("c-draft", draftVersion.id, "#111111", "#222222",
      imageUpdatedBy = Some("draft-editor"), imageUpdatedAt = Some(draftTime))
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(draftCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act
    val result = service.getDraft(handbookId)

    // Assert: draft should show its own image audit
    assert(result.isDefined)
    val dto = result.get
    assert(dto.customization.imageUpdatedBy.contains("draft-editor"))
    assert(dto.customization.imageUpdatedAt.contains(draftTime))
  }

  test("getDraft shows draft audit: header updated by draft editor") {
    // Scenario: Draft editor changes welcome header in draft
    // Draft page should show: Last updated by: draft-editor | current time

    val draftTime = new DateTime(2025, 3, 17, 13, 20).getMillis

    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    // Draft customization with its own header audit
    val draftCustomization = WelcomePageCustomization("c-draft", draftVersion.id, "#111111", "#222222",
      welcomeHeaderUpdatedBy = Some("draft-editor"), welcomeHeaderUpdatedAt = Some(draftTime))
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(draftCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act
    val result = service.getDraft(handbookId)

    // Assert: draft should show its own header audit
    assert(result.isDefined)
    val dto = result.get
    assert(dto.customization.welcomeHeaderUpdatedBy.contains("draft-editor"))
    assert(dto.customization.welcomeHeaderUpdatedAt.contains(draftTime))
  }

  test("getDraft shows draft audit: text updated by draft editor") {
    // Scenario: Draft editor changes welcome text in draft
    // Draft page should show: Last updated by: draft-editor | current time

    val draftTime = new DateTime(2025, 3, 18, 15, 30).getMillis

    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    // Draft customization with its own text audit
    val draftCustomization = WelcomePageCustomization("c-draft", draftVersion.id, "#111111", "#222222",
      welcomeTextUpdatedBy = Some("draft-editor"), welcomeTextUpdatedAt = Some(draftTime))
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(draftCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act
    val result = service.getDraft(handbookId)

    // Assert: draft should show its own text audit
    assert(result.isDefined)
    val dto = result.get
    assert(dto.customization.welcomeTextUpdatedBy.contains("draft-editor"))
    assert(dto.customization.welcomeTextUpdatedAt.contains(draftTime))
  }

  test("getDraft shows draft audit: custom link updated by draft editor") {
    // Scenario: Draft editor adds/edits a custom link in draft
    // Draft page should show: Last updated by: draft-editor | current time

    val draftTime = new DateTime(2025, 3, 19, 10, 15).getMillis

    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    // Draft customization and collections with their own audit
    val draftCustomization = WelcomePageCustomization("c-draft", draftVersion.id, "#111111", "#222222")
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(draftCustomization))

    val draftLinkColl = WelcomePageLinkCollection("lc-draft", draftVersion.id, "External Links", 1, Some("draft-editor"), Some(draftTime))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(List(draftLinkColl))
    when(repo.findLinks("lc-draft")).thenReturn(List(WelcomePageLink("l1", "lc-draft", "Link A", "https://a.com", None, 1)))
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act
    val result = service.getDraft(handbookId)

    // Assert: draft should show its own link collection audit
    assert(result.isDefined)
    val dto = result.get
    assert(dto.linkCollections.head.lastUpdatedBy.contains("draft-editor"))
    assert(dto.linkCollections.head.lastUpdatedAt.contains(draftTime))
  }

  test("getDraft shows draft audit: shortcut collection updated by draft editor") {
    // Scenario: Draft editor edits the shortcut collection in draft
    // Draft page should show: Last updated by: draft-editor | current time

    val draftTime = new DateTime(2025, 3, 20, 12, 45).getMillis

    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    // Draft customization and collections with their own audit
    val draftCustomization = WelcomePageCustomization("c-draft", draftVersion.id, "#111111", "#222222")
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(draftCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)

    val draftShortcutColl = WelcomePageShortcutCollection("sc-draft", draftVersion.id, "Quick Access", 0, 1, Some("draft-editor"), Some(draftTime))
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(List(draftShortcutColl))
    when(repo.findShortcuts("sc-draft")).thenReturn(List(WelcomePageShortcut("s1", "sc-draft", "Shortcut A", None, 1, None, Some("hb1"), None, None)))

    // Act
    val result = service.getDraft(handbookId)

    // Assert: draft should show its own shortcut collection audit
    assert(result.isDefined)
    val dto = result.get
    assert(dto.shortcutCollections.head.lastUpdatedBy.contains("draft-editor"))
    assert(dto.shortcutCollections.head.lastUpdatedAt.contains(draftTime))
  }

  test("getDraft shows draft audit: multiple draft edits show latest draft metadata") {
    // Scenario: A user (e.g., Kushan) makes multiple draft changes
    // Draft page should show the latest draft metadata

    val draftEditTime = new DateTime(2025, 3, 20, 14, 0).getMillis

    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    // Draft has been edited multiple times - should show latest draft audit
    val draftCustomization = WelcomePageCustomization(
      "c-draft", draftVersion.id, "#999999", "#888888", // Different colors from published
      colorUpdatedBy = Some("kushan"), colorUpdatedAt = Some(draftEditTime) // Latest draft edit audit
    )
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(draftCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act
    val result = service.getDraft(handbookId)

    // Assert: draft should show its own audit
    assert(result.isDefined)
    val dto = result.get
    assert(dto.customization.colorUpdatedBy.contains("kushan"))
    assert(dto.customization.colorUpdatedAt.contains(draftEditTime))
  }

  test("getDraft shows draft audit: concurrent changes show draft section metadata") {
    // Scenario: Multiple users edit different sections in draft mode
    // Draft should show its own audit for each section that was edited

    val kushanColorTime = new DateTime(2025, 3, 20, 10, 0).getMillis
    val tharinduImageTime = new DateTime(2025, 3, 21, 14, 0).getMillis
    val nuwanHeaderTime = new DateTime(2025, 3, 22, 9, 0).getMillis

    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    // Draft has multiple concurrent edits with their own audit
    val draftCustomization = WelcomePageCustomization(
      "c-draft", draftVersion.id, "#111111", "#222222",
      colorUpdatedBy = Some("kushan"), colorUpdatedAt = Some(kushanColorTime),
      imageUpdatedBy = Some("tharindu"), imageUpdatedAt = Some(tharinduImageTime),
      welcomeHeaderUpdatedBy = Some("nuwan"), welcomeHeaderUpdatedAt = Some(nuwanHeaderTime),
      welcomeTextUpdatedBy = None, welcomeTextUpdatedAt = None // Text never edited in draft
    )
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(draftCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act
    val result = service.getDraft(handbookId)

    // Assert: draft should show its own audit for each section
    assert(result.isDefined)
    val dto = result.get
    assert(dto.customization.colorUpdatedBy.contains("kushan"))
    assert(dto.customization.colorUpdatedAt.contains(kushanColorTime))
    assert(dto.customization.imageUpdatedBy.contains("tharindu"))
    assert(dto.customization.imageUpdatedAt.contains(tharinduImageTime))
    assert(dto.customization.welcomeHeaderUpdatedBy.contains("nuwan"))
    assert(dto.customization.welcomeHeaderUpdatedAt.contains(nuwanHeaderTime))
    assert(dto.customization.welcomeTextUpdatedBy.isEmpty)
    assert(dto.customization.welcomeTextUpdatedAt.isEmpty)
  }

  // ===== COMPREHENSIVE DRAFT-PUBLISH-ARCHIVE LIFECYCLE TESTS =====

  test("Scenario 1: Default values - initial load from config") {
    // Scenario: Initially load default values for all handbooks from application config
    // Verify that initial reading pages render with these default values
    // Shortcuts should not be shown initially until explicitly configured

    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(None)
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(None)

    // Act: Get published (should return None since no published version exists)
    val publishedResult = service.getPublished(handbookId)

    // Assert: No published version exists initially
    assert(publishedResult.isEmpty)

    // Act: Create first draft (should use defaults)
    when(repo.createVersion(handbookId, VersionStatus.DRAFT, Some("creator"))).thenReturn(draftVersion)
    when(repo.createCustomization(any[WelcomePageCustomization])).thenAnswer(invocation =>
      invocation.getArguments.apply(0).asInstanceOf[WelcomePageCustomization].copy(id = "c-new"))
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(WelcomePageCustomization(
      "c-new", draftVersion.id, "#005A9C", "#FFFFFF", // Default colors from config
      welcomeHeader = Some("Welcome to the Handbook"), // Default header from config
      welcomeText = Some("This is the default welcome message for your handbook.") // Default text from config
    )))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil) // No collections initially
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil) // No shortcuts initially

    val draftResult = service.createDraft(handbookId, "creator")

    // Assert: Draft created with default values from config
    assert(draftResult.customization.primaryColor == "#005A9C") // Default from config
    assert(draftResult.customization.secondaryColor == "#FFFFFF") // Default from config
    assert(draftResult.customization.welcomeHeader.contains("Welcome to the Handbook"))
    assert(draftResult.customization.welcomeText.contains("This is the default welcome message for your handbook."))
    assert(draftResult.linkCollections.isEmpty) // No links initially
    assert(draftResult.shortcutCollections.isEmpty) // No shortcuts initially
  }

  test("Scenario 2: Editor Portal entry - loads from latest published version") {
    // Scenario: When user clicks "Editor Portal" for the first time
    // The customization page should load from the latest published version
    // Draft changes should not touch database until "SAVE AS DRAFT" is clicked

    val publishedTime = new DateTime(2025, 3, 1, 10, 0).getMillis

    // Setup: Published version exists
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(publishedVersion))
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(None)

    val publishedCustomization = WelcomePageCustomization(
      "c-pub", publishedVersion.id, "#123456", "#ABCDEF",
      welcomeHeader = Some("Published Header"),
      welcomeText = Some("Published Text"),
      colorUpdatedBy = Some("previous-user"), colorUpdatedAt = Some(publishedTime)
    )
    when(repo.findCustomization(publishedVersion.id)).thenReturn(Some(publishedCustomization))
    when(repo.findLinkCollections(publishedVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(publishedVersion.id)).thenReturn(Nil)

    // Act: Get published version (what editor portal should load)
    val editorPortalData = service.getPublished(handbookId)

    // Assert: Editor portal loads published version
    assert(editorPortalData.isDefined)
    val dto = editorPortalData.get
    assert(dto.customization.primaryColor == "#123456")
    assert(dto.customization.welcomeHeader.contains("Published Header"))
    assert(dto.customization.colorUpdatedBy.contains("previous-user"))
    assert(dto.customization.colorUpdatedAt.contains(publishedTime))

    // Verify no draft exists yet (user hasn't clicked "SAVE AS DRAFT")
    val draftCheck = service.getDraft(handbookId)
    assert(draftCheck.isEmpty)
  }

  test("Scenario 3: Save as Draft - all changes saved to database") {
    // Scenario: On clicking "SAVE AS DRAFT"
    // Verify all changes (images, collections, colors, text, links, shortcuts) are saved as draft
    // Verify drafts can be further modified before publishing

    // Setup: Published version exists
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(publishedVersion))
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(None)

    val publishedCustomization = WelcomePageCustomization("c-pub", publishedVersion.id, "#000000", "#FFFFFF")
    when(repo.findCustomization(publishedVersion.id)).thenReturn(Some(publishedCustomization))
    when(repo.findLinkCollections(publishedVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(publishedVersion.id)).thenReturn(Nil)

    // Act: Create draft (equivalent to "SAVE AS DRAFT")
    when(repo.createVersion(handbookId, VersionStatus.DRAFT, Some("editor"))).thenReturn(draftVersion)
    when(repo.createCustomization(any[WelcomePageCustomization])).thenAnswer(invocation =>
      invocation.getArguments.apply(0).asInstanceOf[WelcomePageCustomization].copy(id = "c-draft"))
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(publishedCustomization.copy(id = "c-draft", versionId = draftVersion.id)))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    val draftResult = service.createDraft(handbookId, "editor")

    // Assert: Draft created and saved to database
    assert(draftResult.versionId == draftVersion.id)
    verify(repo, times(1)).createVersion(handbookId, VersionStatus.DRAFT, Some("editor"))
    verify(repo, times(1)).createCustomization(any[WelcomePageCustomization])

    // Verify draft can be further modified
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))
    when(repo.updateCustomization(any[WelcomePageCustomization])).thenAnswer(invocation =>
      invocation.getArguments.apply(0).asInstanceOf[WelcomePageCustomization])

    val modifiedDto = draftResult.copy(customization = draftResult.customization.copy(primaryColor = "#FF0000"))
    val updateResult = service.updateCustomization(handbookId, modifiedDto, "editor")

    // Assert: Draft can be modified
    assert(updateResult.isDefined)
    verify(repo, times(1)).updateCustomization(any[WelcomePageCustomization])
  }

  test("Scenario 4: Publishing drafts - reading page renders published version") {
    // Scenario: When user publishes a draft
    // Verify reading page renders with published version
    // Verify customization page loads from latest published version
    // Confirm lastUpdatedBy/At fields update only on publish

    val publishTime = new DateTime(2025, 3, 15, 14, 30).getMillis

    // Setup: Draft exists with changes
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(None) // No previous published

    val draftCustomization = WelcomePageCustomization(
      "c-draft", draftVersion.id, "#FF0000", "#00FF00",
      welcomeHeader = Some("Draft Header"),
      colorUpdatedBy = Some("editor"), colorUpdatedAt = Some(publishTime)
    )
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(draftCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act: Publish draft
    val publishedVersionAfterPublish = draftVersion.copy(status = VersionStatus.PUBLISHED)
    val publishResult = service.publishDraft(handbookId)

    // Assert: Publish successful
    assert(publishResult.isDefined)
    verify(repo, times(1)).publishVersion(draftVersion.id, None)

    // Verify reading page now shows published version
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(publishedVersionAfterPublish))
    when(repo.findCustomization(publishedVersionAfterPublish.id)).thenReturn(Some(draftCustomization.copy(versionId = publishedVersionAfterPublish.id)))

    val readingPageData = service.getPublished(handbookId)
    assert(readingPageData.isDefined)
    assert(readingPageData.get.customization.primaryColor == "#FF0000")
    assert(readingPageData.get.customization.welcomeHeader.contains("Draft Header"))
  }

  test("Scenario 5: Version management - DRAFT → PUBLISHED → ARCHIVED") {
    // Scenario: Ensure all versions (DRAFT, PUBLISHED, ARCHIVED) are managed correctly
    // Verify that we can clear ARCHIVED versions safely without affecting DRAFT or PUBLISHED

    val firstPublishTime = new DateTime(2025, 3, 1, 10, 0).getMillis
    val secondPublishTime = new DateTime(2025, 3, 15, 14, 0).getMillis

    // Setup: First published version exists
    val firstPublishedVersion = WelcomePageVersion("v-pub-1", handbookId, VersionStatus.PUBLISHED, firstPublishTime, firstPublishTime, Some("user1"))
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(firstPublishedVersion))
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    // Mock the required data for publishDraft to work
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(WelcomePageCustomization("c-draft", draftVersion.id, "#000000", "#FFFFFF")))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act: Publish new draft (should archive previous published version)
    service.publishDraft(handbookId)

    // Assert: Previous published version should be archived, draft becomes published
    verify(repo, times(1)).publishVersion(draftVersion.id, Some(firstPublishedVersion.id))

    // The repository method should:
    // 1. Archive the previous published version (v-pub-1 → ARCHIVED)
    // 2. Promote the draft to published (v-draft → PUBLISHED)

    // This verifies the version lifecycle: DRAFT → PUBLISHED → ARCHIVED
  }

  test("Scenario 6: Section-specific updates - only changed sections update metadata") {
    // Scenario: Color updated by User A, Image by User B, etc.
    // Only the changed section should have its metadata updated on publish

    val userATime = new DateTime(2025, 3, 10, 10, 0).getMillis
    val userBTime = new DateTime(2025, 3, 12, 14, 0).getMillis

    // Setup: Published version with existing metadata
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(publishedVersion))
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val publishedCustomization = WelcomePageCustomization(
      "c-pub", publishedVersion.id, "#000000", "#FFFFFF",
      welcomeHeader = Some("Original Header"),
      welcomeText = Some("Original Text"),
      colorUpdatedBy = Some("userA"), colorUpdatedAt = Some(userATime),
      welcomeHeaderUpdatedBy = Some("userB"), welcomeHeaderUpdatedAt = Some(userBTime)
    )
    when(repo.findCustomization(publishedVersion.id)).thenReturn(Some(publishedCustomization))
    when(repo.findLinkCollections(publishedVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(publishedVersion.id)).thenReturn(Nil)

    // Draft has current state
    val currentDraftCustomization = WelcomePageCustomization(
      "c-draft", draftVersion.id, "#000000", "#FFFFFF", // Current colors
      welcomeHeader = Some("Original Header"),
      welcomeText = Some("Original Text")
    )
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(currentDraftCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.updateCustomization(any[WelcomePageCustomization])).thenAnswer(invocation =>
      invocation.getArguments.apply(0).asInstanceOf[WelcomePageCustomization])

    // Mock the existing collections for change detection
    doNothing().when(repo).deleteLinkCollections(draftVersion.id)
    doNothing().when(repo).deleteShortcutCollections(draftVersion.id)

    // Act: Update customization (only color changed)
    val newCustomization = currentDraftCustomization.copy(primaryColor = "#FF0000") // Color change
    val updatedDto = WelcomePageDto(
      draftVersion.id, newCustomization,
      linkCollections = Seq.empty, shortcutCollections = Seq.empty
    )
    service.updateCustomization(handbookId, updatedDto, "userC")

    // Assert: Only color metadata should be updated, header metadata preserved
    val customizationCaptor = ArgumentCaptor.forClass(classOf[WelcomePageCustomization])
    verify(repo, times(1)).updateCustomization(customizationCaptor.capture())

    val updatedCustomization = customizationCaptor.getValue
    assert(updatedCustomization.colorUpdatedBy.contains("userC")) // Color updated by userC
    assert(updatedCustomization.welcomeHeaderUpdatedBy.isEmpty) // Header metadata not updated in draft
    assert(updatedCustomization.welcomeTextUpdatedBy.isEmpty) // Text metadata not updated in draft
  }

  test("Scenario 7: Multiple draft edits without publishing preserve published metadata") {
    // Scenario: User creates several draft edits but does not publish
    // Reading page still shows last published version
    // Metadata remains unchanged until publish

    val originalPublishTime = new DateTime(2025, 3, 1, 10, 0).getMillis
    val draftEditTime1 = new DateTime(2025, 3, 10, 14, 0).getMillis
    val draftEditTime2 = new DateTime(2025, 3, 15, 16, 0).getMillis

    // Setup: Published version exists
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(publishedVersion))
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val publishedCustomization = WelcomePageCustomization(
      "c-pub", publishedVersion.id, "#000000", "#FFFFFF",
      welcomeHeader = Some("Published Header"),
      colorUpdatedBy = Some("original-publisher"), colorUpdatedAt = Some(originalPublishTime)
    )
    when(repo.findCustomization(publishedVersion.id)).thenReturn(Some(publishedCustomization))
    when(repo.findLinkCollections(publishedVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(publishedVersion.id)).thenReturn(Nil)

    // Draft has multiple edits but not published
    val draftCustomization = WelcomePageCustomization(
      "c-draft", draftVersion.id, "#FF0000", "#00FF00", // Multiple changes
      welcomeHeader = Some("Draft Header Changed Twice"),
      colorUpdatedBy = Some("draft-editor"), colorUpdatedAt = Some(draftEditTime2) // Latest draft edit
    )
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(draftCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act: Get reading page (published)
    val readingPageData = service.getPublished(handbookId)

    // Assert: Reading page shows published version, not draft changes
    assert(readingPageData.isDefined)
    val publishedDto = readingPageData.get
    assert(publishedDto.customization.primaryColor == "#000000") // Published color
    assert(publishedDto.customization.welcomeHeader.contains("Published Header")) // Published header
    assert(publishedDto.customization.colorUpdatedBy.contains("original-publisher")) // Published metadata
    assert(publishedDto.customization.colorUpdatedAt.contains(originalPublishTime)) // Published timestamp

    // Act: Get draft (should show draft metadata)
    val draftData = service.getDraft(handbookId)

    // Assert: Draft shows its own metadata, not published metadata
    assert(draftData.isDefined)
    val draftDto = draftData.get
    assert(draftDto.customization.colorUpdatedBy.contains("draft-editor")) // Draft's own metadata
    assert(draftDto.customization.colorUpdatedAt.contains(draftEditTime2)) // Draft's own timestamp
  }

  test("Scenario 8: Concurrent drafts by multiple users") {
    // Scenario: User A edits and saves draft, User B edits and saves draft afterward
    // Ensure system handles latest draft state consistently
    // Only publishing should update metadata, regardless of multiple drafts

    val userAEditTime = new DateTime(2025, 3, 10, 10, 0).getMillis
    val userBEditTime = new DateTime(2025, 3, 12, 14, 0).getMillis
    val originalPublishTime = new DateTime(2025, 2, 15, 9, 0).getMillis

    // Setup: Published version exists
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(publishedVersion))
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val publishedCustomization = WelcomePageCustomization(
      "c-pub", publishedVersion.id, "#000000", "#FFFFFF",
      colorUpdatedBy = Some("original-user"), colorUpdatedAt = Some(originalPublishTime)
    )
    when(repo.findCustomization(publishedVersion.id)).thenReturn(Some(publishedCustomization))
    when(repo.findLinkCollections(publishedVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(publishedVersion.id)).thenReturn(Nil)

    // Draft reflects latest state (User B's changes override User A's)
    val latestDraftCustomization = WelcomePageCustomization(
      "c-draft", draftVersion.id, "#0000FF", "#FFFF00", // User B's changes
      colorUpdatedBy = Some("userB"), colorUpdatedAt = Some(userBEditTime) // Draft metadata (now displayed)
    )
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(latestDraftCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Act: Get draft
    val draftData = service.getDraft(handbookId)

    // Assert: Draft shows latest content and its own metadata
    assert(draftData.isDefined)
    val draftDto = draftData.get
    assert(draftDto.customization.primaryColor == "#0000FF") // Latest draft content (User B)
    assert(draftDto.customization.colorUpdatedBy.contains("userB")) // Draft's own metadata
    assert(draftDto.customization.colorUpdatedAt.contains(userBEditTime)) // Draft's own timestamp

    // Verify that draft shows User B's metadata (latest draft editor)
    assert(!draftDto.customization.colorUpdatedBy.contains("userA"))
    assert(!draftDto.customization.colorUpdatedBy.contains("original-user"))
  }

  test("Scenario 9: Partial section updates - only relevant metadata updates") {
    // Scenario: Draft only changes Welcome Text but not Header
    // Publishing should only update metadata for Welcome Text, Header metadata remains from previous

    val headerPublishTime = new DateTime(2025, 2, 10, 10, 0).getMillis
    val textPublishTime = new DateTime(2025, 3, 15, 14, 0).getMillis

    // Setup: Published version with different metadata for different sections
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(publishedVersion))
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val publishedCustomization = WelcomePageCustomization(
      "c-pub", publishedVersion.id, "#000000", "#FFFFFF",
      welcomeHeader = Some("Original Header"),
      welcomeText = Some("Original Text"),
      welcomeHeaderUpdatedBy = Some("headerUser"), welcomeHeaderUpdatedAt = Some(headerPublishTime),
      welcomeTextUpdatedBy = Some("textUser"), welcomeTextUpdatedAt = Some(textPublishTime)
    )
    when(repo.findCustomization(publishedVersion.id)).thenReturn(Some(publishedCustomization))
    when(repo.findLinkCollections(publishedVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(publishedVersion.id)).thenReturn(Nil)

    // Draft current state
    val currentDraftCustomization = WelcomePageCustomization(
      "c-draft", draftVersion.id, "#000000", "#FFFFFF",
      welcomeHeader = Some("Original Header"),
      welcomeText = Some("Original Text") // Current text
    )
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(currentDraftCustomization))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)
    when(repo.updateCustomization(any[WelcomePageCustomization])).thenAnswer(invocation =>
      invocation.getArguments.apply(0).asInstanceOf[WelcomePageCustomization])

    // Mock the existing collections for change detection
    doNothing().when(repo).deleteLinkCollections(draftVersion.id)
    doNothing().when(repo).deleteShortcutCollections(draftVersion.id)

    // Act: Update customization (only text changed)
    val newCustomization = currentDraftCustomization.copy(welcomeText = Some("Modified Text")) // Text change
    val updatedDto = WelcomePageDto(
      draftVersion.id, newCustomization,
      linkCollections = Seq.empty, shortcutCollections = Seq.empty
    )
    service.updateCustomization(handbookId, updatedDto, "newTextUser")

    // Assert: Only text metadata should be updated
    val customizationCaptor = ArgumentCaptor.forClass(classOf[WelcomePageCustomization])
    verify(repo, times(1)).updateCustomization(customizationCaptor.capture())

    val updatedCustomization = customizationCaptor.getValue
    assert(updatedCustomization.welcomeTextUpdatedBy.contains("newTextUser")) // Text updated
    assert(updatedCustomization.welcomeHeaderUpdatedBy.isEmpty) // Header metadata not updated in draft
  }

  test("Scenario 10: Shortcut/Link collection reordering updates metadata") {
    // Scenario: Reordering items in shortcuts or links should count as an update
    // Metadata should reflect the last user and time who performed the reorder once published

    val originalTime = new DateTime(2025, 3, 10, 10, 0).getMillis
    val reorderTime = new DateTime(2025, 3, 20, 14, 0).getMillis

    // Setup: Published version with link collection
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(publishedVersion))
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val publishedCustomization = WelcomePageCustomization("c-pub", publishedVersion.id, "#000000", "#FFFFFF")
    when(repo.findCustomization(publishedVersion.id)).thenReturn(Some(publishedCustomization))
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(publishedCustomization.copy(id = "c-draft", versionId = draftVersion.id)))

    val originalLinkColl = WelcomePageLinkCollection("lc-pub", publishedVersion.id, "External Links", 1, Some("originalUser"), Some(originalTime))
    when(repo.findLinkCollections(publishedVersion.id)).thenReturn(List(originalLinkColl))
    when(repo.findShortcutCollections(publishedVersion.id)).thenReturn(Nil)

    // Draft has current collection with original order
    val draftLinkColl = WelcomePageLinkCollection("lc-draft", draftVersion.id, "External Links", 1, Some("originalUser"), Some(originalTime))
    when(repo.findLinkCollections(draftVersion.id)).thenReturn(List(draftLinkColl))
    when(repo.findLinks("lc-draft")).thenReturn(List(
      WelcomePageLink("l1", "lc-draft", "Link A", "https://a.com", None, 1), // Original position 1
      WelcomePageLink("l2", "lc-draft", "Link B", "https://b.com", None, 2) // Original position 2
    ))
    when(repo.findShortcutCollections(draftVersion.id)).thenReturn(Nil)

    // Mock repository operations
    when(repo.createLinkCollection(draftVersion.id, "External Links", 1))
      .thenReturn(WelcomePageLinkCollection("lc-new", draftVersion.id, "External Links", 1))
    when(repo.createLink(any[String], any[String], any[String], any[Option[String]], any[Int]))
      .thenReturn(WelcomePageLink("new-link", "lc-new", "title", "url", None, 1))
    when(repo.updateLinkCollection(any[WelcomePageLinkCollection])).thenAnswer(invocation => {
      val input = invocation.getArguments.apply(0).asInstanceOf[WelcomePageLinkCollection]
      // Simulate the service updating the audit fields
      input.copy(lastUpdatedBy = Some("reorderUser"), lastUpdatedAt = Some(DateTime.now().getMillis))
    })
    doNothing().when(repo).deleteLinkCollections(draftVersion.id)
    doNothing().when(repo).deleteShortcutCollections(draftVersion.id)
    when(repo.updateCustomization(any[WelcomePageCustomization])).thenAnswer(invocation =>
      invocation.getArguments.apply(0).asInstanceOf[WelcomePageCustomization])

    // Act: Update with reordered links
    val reorderedDto = WelcomePageDto(
      draftVersion.id, publishedCustomization.copy(id = "c-draft", versionId = draftVersion.id),
      linkCollections = Seq(WelcomePageLinkCollectionDto("lc-draft", "External Links", 1, Seq(
        WelcomePageLink("l2", "lc-draft", "Link B", "https://b.com", None, 1), // Reordered
        WelcomePageLink("l1", "lc-draft", "Link A", "https://a.com", None, 2) // Reordered
      ))),
      shortcutCollections = Seq.empty
    )
    service.updateCustomization(handbookId, reorderedDto, "reorderUser")

    // Assert: Collection metadata should be updated due to reordering
    val collectionCaptor = ArgumentCaptor.forClass(classOf[WelcomePageLinkCollection])
    verify(repo, times(1)).updateLinkCollection(collectionCaptor.capture())

    val updatedCollection = collectionCaptor.getValue
    assert(updatedCollection.lastUpdatedBy.contains("reorderUser")) // Reorder user recorded
    assert(updatedCollection.lastUpdatedAt.exists(_ >= reorderTime)) // Recent timestamp
  }

  test("Scenario 11: Draft discard leaves metadata unchanged") {
    // Scenario: If draft is discarded without publishing, verify it does not affect database metadata
    // Customization page reloads from published version

    val publishedTime = new DateTime(2025, 3, 10, 10, 0).getMillis

    // Setup: Published and draft versions exist
    when(repo.findVersion(handbookId, VersionStatus.PUBLISHED)).thenReturn(Some(publishedVersion))
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(Some(draftVersion))

    val publishedCustomization = WelcomePageCustomization(
      "c-pub", publishedVersion.id, "#000000", "#FFFFFF",
      colorUpdatedBy = Some("publishedUser"), colorUpdatedAt = Some(publishedTime)
    )
    when(repo.findCustomization(publishedVersion.id)).thenReturn(Some(publishedCustomization))
    when(repo.findLinkCollections(publishedVersion.id)).thenReturn(Nil)
    when(repo.findShortcutCollections(publishedVersion.id)).thenReturn(Nil)

    // Act: Discard draft
    when(repo.findCustomization(draftVersion.id)).thenReturn(Some(WelcomePageCustomization("c-draft", draftVersion.id, "#FF0000", "#00FF00")))
    doNothing().when(repo).deleteVersion(draftVersion.id)

    val discardResult = service.discardDraft(handbookId)

    // Assert: Draft discarded successfully
    assert(discardResult)
    verify(repo, times(1)).deleteVersion(draftVersion.id)

    // Verify published version unchanged
    val publishedData = service.getPublished(handbookId)
    assert(publishedData.isDefined)
    assert(publishedData.get.customization.colorUpdatedBy.contains("publishedUser"))
    assert(publishedData.get.customization.colorUpdatedAt.contains(publishedTime))

    // Verify no draft exists after discard
    when(repo.findVersion(handbookId, VersionStatus.DRAFT)).thenReturn(None)
    val draftCheck = service.getDraft(handbookId)
    assert(draftCheck.isEmpty)
  }


}

