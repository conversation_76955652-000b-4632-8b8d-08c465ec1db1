package no.kf.handboker.model.welcomepage

sealed trait VersionStatus
object VersionStatus {
  case object DRAFT extends VersionStatus { override def toString = "DRAFT" }
  case object PUBLISHED extends VersionStatus { override def toString = "PUBLISHED" }
  case object ARCHIVED extends VersionStatus { override def toString = "ARCHIVED" }

  def fromString(status: String): VersionStatus = status match {
    case "DRAFT" => DRAFT
    case "PUBLISHED" => PUBLISHED
    case "ARCHIVED" => ARCHIVED
    case _ => throw new IllegalArgumentException(s"Unknown status: $status")
  }
}
