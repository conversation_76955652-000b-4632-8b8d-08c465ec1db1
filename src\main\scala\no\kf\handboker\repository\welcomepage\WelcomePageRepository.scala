package no.kf.handboker.repository.welcomepage

import no.kf.handboker.model.welcomepage._
import no.kf.handboker.repository.DbConnectionManagerComponent
import no.kf.db.RichSQL._
import no.kf.util.Logging

trait WelcomePageRepositoryComponent {
  this: DbConnectionManagerComponent =>

  val welcomePageRepository: WelcomePageRepository

  class WelcomePageRepositoryImpl extends WelcomePageRepository with Logging {

    private def toVersion(rs: RichResultSet): WelcomePageVersion = {
      WelcomePageVersion(
        id = rs,
        handbookId = rs,
        status = VersionStatus.fromString(rs),
        createdAt = rs,
        updatedAt = rs,
        createdBy = rs
      )
    }

    private def toCustomization(rs: RichResultSet): WelcomePageCustomization = {
      WelcomePageCustomization(
        id = rs,
        versionId = rs,
        primaryColor = rs,
        secondaryColor = rs,
        welcomeImageUrl = rs,
        imageTitle = rs,
        altTitle = rs,
        imageCredits = rs,
        welcomeHeader = rs,
        welcomeText = rs,
        colorUpdatedBy = Option(rs.getString("color_updated_by")).filter(_.nonEmpty),
        colorUpdatedAt = Option(rs.getLong("color_updated_at")).filter(_ != 0),
        imageUpdatedBy = Option(rs.getString("image_updated_by")).filter(_.nonEmpty),
        imageUpdatedAt = Option(rs.getLong("image_updated_at")).filter(_ != 0),
        welcomeHeaderUpdatedBy = Option(rs.getString("welcome_header_updated_by")).filter(_.nonEmpty),
        welcomeHeaderUpdatedAt = Option(rs.getLong("welcome_header_updated_at")).filter(_ != 0),
        welcomeTextUpdatedBy = Option(rs.getString("welcome_text_updated_by")).filter(_.nonEmpty),
        welcomeTextUpdatedAt = Option(rs.getLong("welcome_text_updated_at")).filter(_ != 0)
      )
    }

    private def toLinkCollection(rs: RichResultSet): WelcomePageLinkCollection = {
      WelcomePageLinkCollection(
        id = rs,
        versionId = rs,
        title = rs,
        sortOrder = rs,
        lastUpdatedBy = Option(rs.getString("last_updated_by")).filter(_.nonEmpty),
        lastUpdatedAt = Option(rs.getLong("last_updated_at")).filter(_ != 0)
      )
    }

    private def toLink(rs: RichResultSet): WelcomePageLink = {
      WelcomePageLink(
        id = rs,
        collectionId = rs,
        title = rs,
        url = rs,
        description = rs,
        sortOrder = rs
      )
    }

    private def toShortcutCollection(rs: RichResultSet): WelcomePageShortcutCollection = {
      WelcomePageShortcutCollection(
        id = rs,
        versionId = rs,
        title = rs,
        disabled = rs,
        sortOrder = rs,
        lastUpdatedBy = Option(rs.getString("last_updated_by")).filter(_.nonEmpty),
        lastUpdatedAt = Option(rs.getLong("last_updated_at")).filter(_ != 0)
      )
    }

    private def toShortcut(rs: RichResultSet): WelcomePageShortcut = {
      WelcomePageShortcut(
        id = rs,
        collectionId = rs,
        title = rs,
        description = rs,
        sortOrder = rs,
        link = rs,
        handbookId = rs,
        sectionId = rs,
        chapterId = rs
      )
    }

    // Version operations
    override def findVersion(handbookId: String, status: VersionStatus): Option[WelcomePageVersion] = {
      val sql = "SELECT * FROM welcome_page_version WHERE handbook_id = ? AND status = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << handbookId << status.toString <<! toVersion
      }.headOption
    }

    override def createVersion(handbookId: String, status: VersionStatus, createdBy: Option[String]): WelcomePageVersion = {
      val sql = "INSERT INTO welcome_page_version (id, handbook_id, status, created_at, updated_at, created_by) VALUES (?, ?, ?, ?, ?, ?)"
      val now = org.joda.time.DateTime.now().getMillis
      val newId = no.kf.db.IDGenerator.generateUniqueId
      connectionManager.doWithConnection {
        _.ps(sql) << newId << handbookId << status.toString << now << now << createdBy.orNull <<!
      }
      WelcomePageVersion(newId, handbookId, status, now, now, createdBy)
    }

    override def updateVersion(version: WelcomePageVersion): WelcomePageVersion = {
      val updatedVersion = version.copy(updatedAt = org.joda.time.DateTime.now().getMillis)
      val sql = "UPDATE welcome_page_version SET status = ?, updated_at = ?, created_by = ? WHERE id = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << updatedVersion.status.toString << updatedVersion.updatedAt << updatedVersion.createdBy.orNull << updatedVersion.id <<!
      }
      updatedVersion
    }

    override def publishVersion(draftVersionId: String, publishedVersionId: Option[String]): Unit = {
      publishedVersionId.foreach { id =>
        // First, find the handbook_id from the published version to be archived
        val handbookIdSql = "SELECT handbook_id FROM welcome_page_version WHERE id = ?"
        val handbookId = connectionManager.doWithConnection {
          _.ps(handbookIdSql) << id <<# { rs: RichResultSet => rs.getString("handbook_id") }
        }.headOption

        handbookId.foreach { hbId =>
          // Delete any existing archived version for this handbook to avoid unique constraint violation
          val deleteArchivedSql = "DELETE FROM welcome_page_version WHERE handbook_id = ? AND status = ?"
          connectionManager.doWithConnection {
            _.ps(deleteArchivedSql) << hbId << VersionStatus.ARCHIVED.toString <<!
          }
        }

        // Now archive the current published version
        val archiveSql = "UPDATE welcome_page_version SET status = ?, updated_at = ? WHERE id = ?"
        connectionManager.doWithConnection {
          _.ps(archiveSql) << VersionStatus.ARCHIVED.toString << org.joda.time.DateTime.now().getMillis << id <<!
        }
      }

      val publishSql = "UPDATE welcome_page_version SET status = ?, updated_at = ? WHERE id = ?"
      connectionManager.doWithConnection {
        _.ps(publishSql) << VersionStatus.PUBLISHED.toString << org.joda.time.DateTime.now().getMillis << draftVersionId <<!
      }
    }

    override def deleteVersion(versionId: String): Unit = {
      // Delete in proper order due to foreign key constraints
      
      // First, delete all shortcuts for all shortcut collections in this version
      val deleteShortcutsSql = """
        DELETE FROM welcome_page_shortcut 
        WHERE collection_id IN (
          SELECT id FROM welcome_page_shortcut_collection WHERE version_id = ?
        )
      """
      connectionManager.doWithConnection {
        _.ps(deleteShortcutsSql) << versionId <<!
      }
      
      // Delete all shortcut collections for this version
      deleteShortcutCollections(versionId)
      
      // Delete all links for all link collections in this version
      val deleteLinksSql = """
        DELETE FROM welcome_page_link 
        WHERE collection_id IN (
          SELECT id FROM welcome_page_link_collection WHERE version_id = ?
        )
      """
      connectionManager.doWithConnection {
        _.ps(deleteLinksSql) << versionId <<!
      }
      
      // Delete all link collections for this version
      deleteLinkCollections(versionId)
      
      // Delete customization for this version
      val deleteCustomizationSql = "DELETE FROM welcome_page_customization WHERE version_id = ?"
      connectionManager.doWithConnection {
        _.ps(deleteCustomizationSql) << versionId <<!
      }
      
      // Finally, delete the version itself
      val deleteVersionSql = "DELETE FROM welcome_page_version WHERE id = ?"
      connectionManager.doWithConnection {
        _.ps(deleteVersionSql) << versionId <<!
      }
    }

    // Customization operations
    override def findCustomization(versionId: String): Option[WelcomePageCustomization] = {
      val sql = "SELECT * FROM welcome_page_customization WHERE version_id = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << versionId <<! toCustomization
      }.headOption
    }

    override def createCustomization(customization: WelcomePageCustomization): WelcomePageCustomization = {
      val sql = """INSERT INTO welcome_page_customization 
                   (id, version_id, primary_color, secondary_color, welcome_image, image_title, alt_title, image_credits, 
                    welcome_header, welcome_text, color_updated_by, color_updated_at, image_updated_by, image_updated_at,
                    welcome_header_updated_by, welcome_header_updated_at, welcome_text_updated_by, welcome_text_updated_at) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"""
      val newId = no.kf.db.IDGenerator.generateUniqueId
      connectionManager.doWithConnection {
        _.ps(sql) << 
          newId << 
          customization.versionId << 
          customization.primaryColor << 
          customization.secondaryColor << 
          customization.welcomeImageUrl.orNull <<
          customization.imageTitle.orNull << 
          customization.altTitle.orNull << 
          customization.imageCredits.orNull << 
          customization.welcomeHeader.orNull << 
          customization.welcomeText.orNull << 
          customization.colorUpdatedBy.orNull << 
          customization.colorUpdatedAt.getOrElse(0L) << 
          customization.imageUpdatedBy.orNull << 
          customization.imageUpdatedAt.getOrElse(0L) << 
          customization.welcomeHeaderUpdatedBy.orNull << 
          customization.welcomeHeaderUpdatedAt.getOrElse(0L) << 
          customization.welcomeTextUpdatedBy.orNull << 
          customization.welcomeTextUpdatedAt.getOrElse(0L) <<!
      }
      customization.copy(id = newId)
    }

    override def updateCustomization(customization: WelcomePageCustomization): WelcomePageCustomization = {
      val sql = """UPDATE welcome_page_customization SET 
                   primary_color = ?, secondary_color = ?, welcome_image = ?, image_title = ?, alt_title = ?, image_credits = ?,
                   welcome_header = ?, welcome_text = ?, color_updated_by = ?, color_updated_at = ?, image_updated_by = ?, 
                   image_updated_at = ?, welcome_header_updated_by = ?, welcome_header_updated_at = ?, welcome_text_updated_by = ?, 
                   welcome_text_updated_at = ? WHERE id = ?"""
      connectionManager.doWithConnection {
        _.ps(sql) << 
          customization.primaryColor << 
          customization.secondaryColor << 
          customization.welcomeImageUrl.orNull <<
          customization.imageTitle.orNull << 
          customization.altTitle.orNull << 
          customization.imageCredits.orNull << 
          customization.welcomeHeader.orNull << 
          customization.welcomeText.orNull << 
          customization.colorUpdatedBy.orNull << 
          customization.colorUpdatedAt.getOrElse(0L) << 
          customization.imageUpdatedBy.orNull << 
          customization.imageUpdatedAt.getOrElse(0L) << 
          customization.welcomeHeaderUpdatedBy.orNull << 
          customization.welcomeHeaderUpdatedAt.getOrElse(0L) << 
          customization.welcomeTextUpdatedBy.orNull << 
          customization.welcomeTextUpdatedAt.getOrElse(0L) << 
          customization.id <<!
      }
      customization
    }

    // Link collection operations
    override def findLinkCollections(versionId: String): List[WelcomePageLinkCollection] = {
      val sql = "SELECT * FROM welcome_page_link_collection WHERE version_id = ? ORDER BY sort_order"
      connectionManager.doWithConnection {
        _.ps(sql) << versionId <<! toLinkCollection
      }
    }

    override def createLinkCollection(versionId: String, title: String, sortOrder: Int): WelcomePageLinkCollection = {
      val sql = "INSERT INTO welcome_page_link_collection (id, version_id, title, sort_order, last_updated_by, last_updated_at) VALUES (?, ?, ?, ?, ?, ?)"
      val newId = no.kf.db.IDGenerator.generateUniqueId
      val now = org.joda.time.DateTime.now().getMillis
      connectionManager.doWithConnection {
        _.ps(sql) << newId << versionId << title << sortOrder << null.asInstanceOf[String] << now <<!
      }
      WelcomePageLinkCollection(newId, versionId, title, sortOrder, None, Some(now))
    }

    override def updateLinkCollection(collection: WelcomePageLinkCollection): WelcomePageLinkCollection = {
      val sql = "UPDATE welcome_page_link_collection SET title = ?, sort_order = ?, last_updated_by = ?, last_updated_at = ? WHERE id = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << collection.title << collection.sortOrder << collection.lastUpdatedBy.orNull << collection.lastUpdatedAt.getOrElse(0L) << collection.id <<!
      }
      collection
    }

    override def deleteLinkCollections(versionId: String): Unit = {
      val sql = "DELETE FROM welcome_page_link_collection WHERE version_id = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << versionId <<!
      }
    }

    // Link operations
    override def findLinks(collectionId: String): List[WelcomePageLink] = {
      val sql = "SELECT * FROM welcome_page_link WHERE collection_id = ? ORDER BY sort_order"
      connectionManager.doWithConnection {
        _.ps(sql) << collectionId <<! toLink
      }
    }

    override def createLink(collectionId: String, title: String, url: String, description: Option[String], sortOrder: Int): WelcomePageLink = {
      val sql = "INSERT INTO welcome_page_link (id, collection_id, title, url, description, sort_order) VALUES (?, ?, ?, ?, ?, ?)"
      val newId = no.kf.db.IDGenerator.generateUniqueId
      connectionManager.doWithConnection {
        _.ps(sql) << newId << collectionId << title << url << description.orNull << sortOrder <<!
      }
      WelcomePageLink(newId, collectionId, title, url, description, sortOrder)
    }

    override def updateLink(link: WelcomePageLink): WelcomePageLink = {
      val sql = "UPDATE welcome_page_link SET title = ?, url = ?, description = ?, sort_order = ? WHERE id = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << link.title << link.url << link.description.orNull << link.sortOrder << link.id <<!
      }
      link
    }

    override def deleteLinks(collectionId: String): Unit = {
      val sql = "DELETE FROM welcome_page_link WHERE collection_id = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << collectionId <<!
      }
    }

    // Shortcut collection operations
    override def findShortcutCollections(versionId: String): List[WelcomePageShortcutCollection] = {
      val sql = "SELECT * FROM welcome_page_shortcut_collection WHERE version_id = ? ORDER BY sort_order"
      connectionManager.doWithConnection {
        _.ps(sql) << versionId <<! toShortcutCollection
      }
    }

    override def createShortcutCollection(versionId: String, title: String, disabled: Boolean, sortOrder: Int): WelcomePageShortcutCollection = {
      val sql = "INSERT INTO welcome_page_shortcut_collection (id, version_id, title, disabled, sort_order, last_updated_by, last_updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)"
      val newId = no.kf.db.IDGenerator.generateUniqueId
      val disabledInt = if (disabled) 1 else 0
      val now = org.joda.time.DateTime.now().getMillis
      connectionManager.doWithConnection {
        _.ps(sql) << newId << versionId << title << disabledInt << sortOrder << null.asInstanceOf[String] << now <<!
      }
      WelcomePageShortcutCollection(newId, versionId, title, disabledInt, sortOrder, None, Some(now))
    }

    override def updateShortcutCollection(collection: WelcomePageShortcutCollection): WelcomePageShortcutCollection = {
      val sql = "UPDATE welcome_page_shortcut_collection SET title = ?, disabled = ?, sort_order = ?, last_updated_by = ?, last_updated_at = ? WHERE id = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << collection.title << collection.disabled << collection.sortOrder << collection.lastUpdatedBy.orNull << collection.lastUpdatedAt.getOrElse(0L) << collection.id <<!
      }
      collection
    }

    override def deleteShortcutCollections(versionId: String): Unit = {
      val sql = "DELETE FROM welcome_page_shortcut_collection WHERE version_id = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << versionId <<!
      }
    }

    // Shortcut operations
    override def findShortcuts(collectionId: String): List[WelcomePageShortcut] = {
      val sql = "SELECT * FROM welcome_page_shortcut WHERE collection_id = ? ORDER BY sort_order"
      connectionManager.doWithConnection {
        _.ps(sql) << collectionId <<! toShortcut
      }
    }

    override def createShortcut(shortcut: WelcomePageShortcut): WelcomePageShortcut = {
      val sql = "INSERT INTO welcome_page_shortcut (id, collection_id, title, description, sort_order, link, handbook_id, section_id, chapter_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"
      val newId = no.kf.db.IDGenerator.generateUniqueId
      connectionManager.doWithConnection {
        _.ps(sql) << 
          newId << 
          shortcut.collectionId << 
          shortcut.title << 
          shortcut.description.orNull << 
          shortcut.sortOrder << 
          shortcut.link.orNull << 
          shortcut.handbookId.orNull << 
          shortcut.sectionId.orNull << 
          shortcut.chapterId.orNull <<!
      }
      shortcut.copy(id = newId)
    }

    override def updateShortcut(shortcut: WelcomePageShortcut): WelcomePageShortcut = {
      val sql = "UPDATE welcome_page_shortcut SET title = ?, description = ?, sort_order = ?, link = ?, handbook_id = ?, section_id = ?, chapter_id = ? WHERE id = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << 
          shortcut.title << 
          shortcut.description.orNull << 
          shortcut.sortOrder << 
          shortcut.link.orNull << 
          shortcut.handbookId.orNull << 
          shortcut.sectionId.orNull << 
          shortcut.chapterId.orNull << 
          shortcut.id <<!
      }
      shortcut
    }

    override def deleteShortcuts(collectionId: String): Unit = {
      val sql = "DELETE FROM welcome_page_shortcut WHERE collection_id = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << collectionId <<!
      }
    }
  }
}

trait WelcomePageRepository {
  // Version operations
  def findVersion(handbookId: String, status: VersionStatus): Option[WelcomePageVersion]
  def createVersion(handbookId: String, status: VersionStatus, createdBy: Option[String]): WelcomePageVersion
  def updateVersion(version: WelcomePageVersion): WelcomePageVersion
  def publishVersion(draftVersionId: String, publishedVersionId: Option[String]): Unit
  def deleteVersion(versionId: String): Unit

  // Customization operations
  def findCustomization(versionId: String): Option[WelcomePageCustomization]
  def createCustomization(customization: WelcomePageCustomization): WelcomePageCustomization
  def updateCustomization(customization: WelcomePageCustomization): WelcomePageCustomization

  // Link collection operations
  def findLinkCollections(versionId: String): List[WelcomePageLinkCollection]
  def createLinkCollection(versionId: String, title: String, sortOrder: Int): WelcomePageLinkCollection
  def updateLinkCollection(collection: WelcomePageLinkCollection): WelcomePageLinkCollection
  def deleteLinkCollections(versionId: String): Unit

  // Link operations
  def findLinks(collectionId: String): List[WelcomePageLink]
  def createLink(collectionId: String, title: String, url: String, description: Option[String], sortOrder: Int): WelcomePageLink
  def updateLink(link: WelcomePageLink): WelcomePageLink
  def deleteLinks(collectionId: String): Unit

  // Shortcut collection operations
  def findShortcutCollections(versionId: String): List[WelcomePageShortcutCollection]
  def createShortcutCollection(versionId: String, title: String, disabled: Boolean, sortOrder: Int): WelcomePageShortcutCollection
  def updateShortcutCollection(collection: WelcomePageShortcutCollection): WelcomePageShortcutCollection
  def deleteShortcutCollections(versionId: String): Unit

  // Shortcut operations
  def findShortcuts(collectionId: String): List[WelcomePageShortcut]
  def createShortcut(shortcut: WelcomePageShortcut): WelcomePageShortcut
  def updateShortcut(shortcut: WelcomePageShortcut): WelcomePageShortcut
  def deleteShortcuts(collectionId: String): Unit
}