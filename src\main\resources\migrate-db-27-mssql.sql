-- Add collection-level audit fields for links and shortcuts (MSSQL)
ALTER TABLE welcome_page_link_collection ADD last_updated_by VA<PERSON><PERSON>R(100) NULL;
ALTER TABLE welcome_page_link_collection ADD last_updated_at BIGINT NULL;

ALTER TABLE welcome_page_shortcut_collection ADD last_updated_by VA<PERSON>HAR(100) NULL;
ALTER TABLE welcome_page_shortcut_collection ADD last_updated_at BIGINT NULL;

-- Add soft delete columns to welcome_page_version table
-- This allows for better audit trails and data recovery for archived versions
ALTER TABLE welcome_page_version ADD deleted SMALLINT NOT NULL DEFAULT 0;
ALTER TABLE welcome_page_version ADD deleted_date BIGINT NULL;

-- Update the unique constraint to only apply to non-deleted records
-- First drop the existing constraint
ALTER TABLE welcome_page_version DROP CONSTRAINT uq_welcome_page_version_handbook_status;

-- Add new filtered unique index that excludes soft-deleted records
-- This allows multiple archived versions as long as only one is not soft-deleted
CREATE UNIQUE INDEX ix_welcome_page_version_handbook_status_active
ON welcome_page_version (handbook_id, status)
WHERE deleted = 0;

