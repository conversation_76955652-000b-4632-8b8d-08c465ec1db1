-- Add collection-level audit fields for links and shortcuts
ALTER TABLE welcome_page_link_collection ADD COLUMN last_updated_by VA<PERSON><PERSON><PERSON>(100);
ALTER TABLE welcome_page_link_collection ADD COLUMN last_updated_at BIGINT;

ALTER TABLE welcome_page_shortcut_collection ADD COLUMN last_updated_by <PERSON><PERSON><PERSON><PERSON>(100);
ALTER TABLE welcome_page_shortcut_collection ADD COLUMN last_updated_at BIGINT;

-- Add soft delete columns to welcome_page_version table
-- This allows for better audit trails and data recovery for archived versions
ALTER TABLE welcome_page_version ADD COLUMN deleted SMALLINT NOT NULL DEFAULT 0;
ALTER TABLE welcome_page_version ADD COLUMN deleted_date BIGINT;

-- Update the unique constraint to only apply to non-deleted records
-- First drop the existing constraint
ALTER TABLE welcome_page_version DROP CONSTRAINT welcome_page_version_handbook_id_status_key;

-- Add new partial unique constraint that excludes soft-deleted records
-- This allows multiple archived versions as long as only one is not soft-deleted
CREATE UNIQUE INDEX welcome_page_version_handbook_status_active
ON welcome_page_version (handbook_id, status)
WHERE deleted = 0;

