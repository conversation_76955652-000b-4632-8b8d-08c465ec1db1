-- Migration 28: Add soft delete support for welcome page versions (SQL Server)
-- This migration adds soft delete functionality to prevent constraint violations
-- when publishing multiple drafts while preserving audit trails

-- Add soft delete columns to welcome_page_version table
ALTER TABLE welcome_page_version ADD deleted SMALLINT NOT NULL DEFAULT 0;
ALTER TABLE welcome_page_version ADD deleted_date BIGINT NULL;

-- Update the unique constraint to only apply to non-deleted records
-- First drop the existing constraint
ALTER TABLE welcome_page_version DROP CONSTRAINT uq_welcome_page_version_handbook_status;

-- Add new filtered unique index that excludes soft-deleted records
-- This allows multiple archived versions as long as only one is not soft-deleted
CREATE UNIQUE INDEX ix_welcome_page_version_handbook_status_active ON welcome_page_version (handbook_id, status) WHERE deleted = 0;

-- Update any existing data to set deleted = 0 (should already be default, but ensure consistency)
UPDATE welcome_page_version SET deleted = 0 WHERE deleted IS NULL;
