package no.kf.handboker.model.welcomepage

case class WelcomePageDto(
  versionId: String,
  customization: WelcomePageCustomization,
  linkCollections: Seq[WelcomePageLinkCollectionDto],
  shortcutCollections: Seq[WelcomePageShortcutCollectionDto]
)

case class WelcomePageLinkCollectionDto(
  id: String,
  title: String,
  sortOrder: Int,
  links: Seq[WelcomePageLink],
  lastUpdatedBy: Option[String] = None,
  lastUpdatedAt: Option[Long] = None
)

case class WelcomePageShortcutCollectionDto(
  id: String,
  title: String,
  disabled: Boolean, // Keep as Boolean for API, convert to Int in repository
  sortOrder: Int,
  shortcuts: Seq[WelcomePageShortcut],
  lastUpdatedBy: Option[String] = None,
  lastUpdatedAt: Option[Long] = None
)
