package no.kf.handboker.rest

import no.kf.handboker.model.welcomepage.VersionStatus
import org.json4s.{DefaultFormats, Formats, JValue, TypeInfo}
import org.json4s.JsonAST.JString

object WelcomePageDtoFormats {
  implicit val versionStatusSerializer = new org.json4s.Serializer[VersionStatus] {
    private val VersionStatusClass = classOf[VersionStatus]

    def deserialize(implicit format: Formats): PartialFunction[(org.json4s.TypeInfo, JValue), VersionStatus] = {
      case (TypeInfo(VersionStatusClass, _), json) => json match {
        case JString(s) => VersionStatus.fromString(s)
        case _ => throw new org.json4s.MappingException("Can't convert " + json + " to VersionStatus")
      }
    }

    def serialize(implicit format: Formats): PartialFunction[Any, JValue] = {
      case status: VersionStatus => JString(status.toString)
    }
  }

  val formats: Formats = DefaultFormats + versionStatusSerializer
}