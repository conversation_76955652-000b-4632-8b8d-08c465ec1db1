# Welcome Page REST API Documentation

## Overview

The Welcome Page API provides endpoints for managing welcome page customizations for handbooks. It supports a draft/publish workflow where users can create and edit drafts before publishing them.

## Base URL
```
/api/welcome-page
```

## Authentication
All endpoints require authentication through the existing session management system.

## Endpoints

### New: Get Defaults for "Set to default" buttons

- GET `/api/welcome-page/defaults`
- Purpose: Provide default values the frontend should load into the draft when the user clicks any of the four "Set to default" buttons (color theme, welcome image, welcome header, welcome text). These values are not persisted until the client calls PUT /draft.

Response body:
```json
{
  "primaryColor": "#005A9C",
  "secondaryColor": "#FFFFFF",
  "welcomeHeader": "Welcome to the Handbook",
  "welcomeText": "This is the default welcome message for your handbook.",
  "welcomeImageResponse": "data:image/png;base64,....", // Base64 data URI, suitable for immediate rendering
  "imageTitle": "Default Image Title",
  "altTitle": "Default Alt Title",
  "imageCredits": "Default image credits"
}
```

Notes:
- welcomeImageResponse is a data URI built server-side from the configured default image file (if configured). This ensures the UI can immediately render the default image without additional fetches.
- If no default image is configured or readable, welcomeImageResponse will be absent (null), and the UI should handle accordingly.


### 1. Get Published Welcome Page

**GET** `/api/welcome-page/published/:handbookId`

Retrieves the currently published welcome page for a handbook.

**Parameters:**
- `handbookId` (path parameter): The ID of the handbook

**Response:**
- **200 OK**: Returns the published welcome page data
- **404 Not Found**: No published welcome page exists for the handbook

**Example Response:**
```json
{
  "versionId": 123,
  "customization": {
    "id": 456,
    "versionId": 123,
    "primaryColor": "#005A9C",
    "secondaryColor": "#FFFFFF",
    "welcomeImageUrl": "image-url",
    "welcomeImageResponse": "data:image/jpeg;base64, ...",
    "imageTitle": "Welcome Image",
    "altTitle": "Alt text",
    "imageCredits": "Photo credits",
    "welcomeHeader": "Welcome to Our Handbook",
    "welcomeText": "This is our comprehensive handbook...",
    "colorUpdatedBy": "<EMAIL>",
    "colorUpdatedAt": "2024-01-15T10:30:00Z",
    "imageUpdatedBy": "<EMAIL>",
    "imageUpdatedAt": "2024-01-15T10:30:00Z",
    "welcomeHeaderUpdatedBy": "<EMAIL>",
    "welcomeHeaderUpdatedAt": "2024-01-15T10:30:00Z",
    "welcomeTextUpdatedBy": "<EMAIL>",
    "welcomeTextUpdatedAt": "2024-01-15T10:30:00Z"
  },
  "linkCollections": [
    {
      "id": 789,
      "title": "External Resources",
      "sortOrder": 1,
      "lastUpdatedBy": "<EMAIL>",
      "lastUpdatedAt": 1705312200000,
      "links": [
        {
          "id": 101,
          "collectionId": 789,
          "title": "Company Website",
          "url": "https://example.com",
          "description": "Our main website",
          "sortOrder": 1
        }
      ]
    }
  ],
  "shortcutCollections": [
    {
      "id": 202,
      "title": "Quick Access",
      "disabled": false,
      "sortOrder": 1,
      "lastUpdatedBy": "<EMAIL>",
      "lastUpdatedAt": 1705398600000,
      "shortcuts": [
        {
          "id": 303,
          "collectionId": 202,
          "title": "Getting Started",
          "description": "Quick start guide",
          "sortOrder": 1,
          "link": null,
          "handbookId": 1,
          "sectionId": "section-123",
          "chapterId": null
        }
      ]
    }
  ]
}
```

### 2. Get Draft Welcome Page

**GET** `/api/welcome-page/draft/:handbookId`

Retrieves the current draft welcome page for a handbook.

**Parameters:**
- `handbookId` (path parameter): The ID of the handbook

**Response:**
- **200 OK**: Returns the draft welcome page data
- **404 Not Found**: No draft exists for the handbook

Notes:
- The audit fields (colorUpdatedBy/At, imageUpdatedBy/At, welcomeHeaderUpdatedBy/At, welcomeTextUpdatedBy/At) in the draft response reflect the LAST PUBLISHED version, not the in-progress draft edits. This matches the UI requirement to show “Last updated by/date” from the published page while editing a draft.
- These audit fields are read-only and computed by the server. Clients must not send them in requests.

**Example Response:** Same structure as the published welcome page.

### 3. Create Draft Welcome Page

**POST** `/api/welcome-page/draft/:handbookId`

Creates a new draft welcome page for a handbook. If a published version exists, it will be copied to the draft. Otherwise, a default welcome page will be created.

**Parameters:**
- `handbookId` (path parameter): The ID of the handbook

**Content Types Supported:**
- `application/json` (default)
- `multipart/form-data` (for image upload during creation)

**Request Body (JSON):**
```json
// No body required for basic draft creation
```

**Request Body (Multipart - Optional Image Upload):**
```
Content-Type: multipart/form-data

file: [image file, max 5MB, formats: jpg, jpeg, png, gif, webp]
```

**Response:**
- **200 OK**: Returns the created draft welcome page data

**Example Response:** Same structure as published welcome page.

### 4. Update Draft Welcome Page

**PUT** `/api/welcome-page/draft/:handbookId`

Updates the draft welcome page customization. Supports both JSON-only updates and combined image + data updates.

**Parameters:**
- `handbookId` (path parameter): The ID of the handbook

**Content Types Supported:**
- `application/json` (for data-only updates)
- `multipart/form-data` (for image upload with optional data)

**Request Body (JSON Only):**
```json
{
  "versionId": 123,
  "customization": {
    "id": 456,
    "versionId": 123,
    "primaryColor": "#FF5733",
    "secondaryColor": "#FFFFFF",
    "welcomeImageUrl": "existing-image-url",
    "imageTitle": "Updated Welcome Image",
    "altTitle": "Updated alt text",
    "imageCredits": "Updated photo credits",
    "welcomeHeader": "Updated Welcome Header",
    "welcomeText": "Updated welcome text..."
  },
  "linkCollections": [
    {
      "id": 789,
      "title": "Updated External Resources",
      "sortOrder": 1,
      "links": [
        {
          "id": 101,
          "collectionId": 789,
          "title": "Updated Company Website",
          "url": "https://newexample.com",
          "description": "Our updated main website",
          "sortOrder": 1
        }
      ]
    }
  ],
  "shortcutCollections": [
    {
      "id": 202,
      "title": "Updated Quick Access",
      "disabled": false,
      "sortOrder": 1,
      "shortcuts": [
        {
          "id": 303,
          "collectionId": 202,
          "title": "Updated Getting Started",
          "description": "Updated quick start guide",
          "sortOrder": 1,
          "link": null,
          "handbookId": 1,
          "sectionId": "section-456",
          "chapterId": null
        }
      ]
    }
  ]
}
```

Note:
- Audit fields (colorUpdatedBy/At, imageUpdatedBy/At, welcomeHeaderUpdatedBy/At, welcomeTextUpdatedBy/At) are read-only and are computed by the server based on what changed in the draft. Do not send them in requests; any values sent will be ignored.
- The welcomeImageUrl cannot be changed via JSON; upload a file using multipart PUT or the dedicated image endpoint to replace the image. Image metadata (title, altTitle, imageCredits) can be changed via JSON and will update the image audit fields.

**Request Body (Multipart - Image + Data):**
```
Content-Type: multipart/form-data

file: [image file, max 5MB, formats: jpg, jpeg, png, gif, webp]
data: [JSON string containing WelcomePageDto as shown above]
```

**Request Body (Multipart - Image Only):**
```
Content-Type: multipart/form-data

file: [image file, max 5MB, formats: jpg, jpeg, png, gif, webp]
```

**Response:**
- **200 OK**: Returns the updated draft welcome page data
- **404 Not Found**: No draft exists for the handbook
- **400 Bad Request**: Invalid image file or data

### 5. Publish Draft Welcome Page

**POST** `/api/welcome-page/publish/:handbookId`

Publishes the current draft welcome page. The existing published version (if any) will be archived, and the draft will become the new published version.

**Parameters:**
- `handbookId` (path parameter): The ID of the handbook

**Response:**
- **200 OK**: Returns the newly published welcome page data
- **404 Not Found**: No draft exists for the handbook

### 5. Discard Draft Welcome Page

**DELETE** `/api/welcome-page/draft/:handbookId`

Discards the current draft welcome page, including any uploaded images. This action cannot be undone.

**Parameters:**
- `handbookId` (path parameter): The ID of the handbook

**Response:**
- **200 OK**: Draft successfully discarded
- **404 Not Found**: No draft exists for the handbook

**Example Response:**
```json
{
  "message": "Draft for handbook 123 has been discarded successfully."
}
```

### 6. Publish Draft Welcome Page

**POST** `/api/welcome-page/publish/:handbookId`

Publishes the current draft welcome page. The existing published version (if any) will be archived, and the draft will become the new published version.

**Parameters:**
- `handbookId` (path parameter): The ID of the handbook

**Response:**
- **200 OK**: Returns the newly published welcome page data
- **404 Not Found**: No draft exists for the handbook

### 7. Upload Image to Draft (Alternative Endpoint)

**POST** `/api/welcome-page/draft/:handbookId/image`

Alternative endpoint for uploading images to a draft welcome page. This is provided for cases where you want to upload images separately from other data updates.

**Parameters:**
- `handbookId` (path parameter): The ID of the handbook

**Request Body:**
```
Content-Type: multipart/form-data

file: [image file, max 5MB, formats: jpg, jpeg, png, gif, webp]
```

**Response:**
- **200 OK**: Image uploaded successfully
- **400 Bad Request**: Invalid image file or no file provided
- **404 Not Found**: No draft exists for the handbook

**Example Response:**
```json
{
  "imageUrl": "/handboker/images/abcd1234-5678-90ef-ghij-klmnopqrstuv.jpg"
}
```

### 8. Remove Image from Draft (Alternative Endpoint)

**DELETE** `/api/welcome-page/draft/:handbookId/image`

Alternative endpoint for removing the image from a draft welcome page.

**Parameters:**
- `handbookId` (path parameter): The ID of the handbook

**Response:**
- **200 OK**: Image removed successfully
- **404 Not Found**: No draft exists for the handbook

**Example Response:**
```json
{
  "message": "Image removed successfully"
}
```

### 9. Get Welcome Page Status

**GET** `/api/welcome-page/status/:handbookId`

Returns the status of welcome page versions for a handbook.

**Parameters:**
- `handbookId` (path parameter): The ID of the handbook

**Response:**
- **200 OK**: Returns status information

**Example Response:**
```json
{
  "handbookId": 1,
  "hasDraft": true,
  "hasPublished": false
}
```

## Image Upload Features

### Supported Image Formats
- **JPEG/JPG**: Standard web image format
- **PNG**: Supports transparency
- **GIF**: Supports animation
- **WebP**: Modern web format with better compression

### Image Validation Rules
- **Maximum file size**: 5MB (5,242,880 bytes)
- **Content type validation**: Must be a valid image MIME type
- **Format validation**: Only supported formats are accepted
- **File extension validation**: Must match content type

### Image Storage
- Images are stored in the file system (not in the database)
- Database stores only the image URL/path reference
- Images are organized in subfolders for efficient storage
- Automatic cleanup when drafts are discarded or images are replaced

### Image URLs
Images are served through the application at URLs like:
```
/handboker/images/abcd1234-5678-90ef-ghij-klmnopqrstuv.jpg
```

### Image Upload Workflows

#### Workflow 1: Integrated Upload (Recommended)
Upload image together with other data in a single request:
```javascript
const formData = new FormData();
formData.append('file', imageFile);
formData.append('data', JSON.stringify(welcomePageData));

await fetch(`/api/welcome-page/draft/${handbookId}`, {
  method: 'PUT',
  body: formData
});
```

#### Workflow 2: Separate Upload
Upload image separately from other data:
```javascript
// First upload image
const imageFormData = new FormData();
imageFormData.append('file', imageFile);

await fetch(`/api/welcome-page/draft/${handbookId}/image`, {
  method: 'POST',
  body: imageFormData
});

// Then update other data
await fetch(`/api/welcome-page/draft/${handbookId}`, {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(welcomePageData)
});
```

#### Using Defaults in UI (four buttons)
```javascript
// 1) Fetch defaults (colors, header, text, and image+metadata)
const defaults = await fetch('/api/welcome-page/defaults').then(r => r.json());

// 2) Apply locally when user clicks a button, e.g. Welcome Image
state.customization = {
  ...state.customization,
  imageTitle: defaults.imageTitle || '',
  altTitle: defaults.altTitle || '',
  imageCredits: defaults.imageCredits || '',
  // For previewing immediately use defaults.welcomeImageResponse (data URI)
};

// 3) Persist only when "Save as Draft" is clicked
await fetch(`/api/welcome-page/draft/${handbookId}`, {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(state)
});
```

### Error Handling for Images

#### Image Validation Errors
```json
{
  "error": "Image file size (7MB) exceeds maximum allowed size of 5MB."
}
```

```json
{
  "error": "Unsupported image format: bmp. Supported formats: jpg, jpeg, png, gif, webp"
}
```

```json
{
  "error": "File must be an image."
}
```

#### Image Processing Errors
```json
{
  "error": "Failed to process uploaded image."
}
```

## Data Models

### WelcomePageDto
The main data transfer object containing all welcome page information.

### WelcomePageCustomization
Contains visual customization settings:
- Colors (primary, secondary)
- Welcome image and metadata
- welcomeImageResponse (optional): Base64 data URI of the welcome image included in GET responses for immediate rendering in the frontend
- Welcome header and text
- Audit fields for tracking changes

### WelcomePageLinkCollection
Groups of external links:
- Title and sort order
- Contains multiple WelcomePageLink objects
- Collection-level audit fields: `lastUpdatedBy`, `lastUpdatedAt` (BIGINT timestamp in milliseconds)
- Audit is updated when collection title/order changes or any contained link is modified/sorted

### WelcomePageLink
Individual external links:
- Title, URL, description
- Sort order within collection

### WelcomePageShortcutCollection
Groups of internal shortcuts:
- Title, disabled status, sort order
- Contains multiple WelcomePageShortcut objects
- Collection-level audit fields: `lastUpdatedBy`, `lastUpdatedAt` (BIGINT timestamp in milliseconds)
- Audit is updated when collection title/order/disabled status changes or any contained shortcut is modified/sorted

### WelcomePageShortcut
Internal navigation shortcuts:
- Can link to external URLs, handbooks, sections, or chapters
- Title, description, sort order

## Audit Behavior

### Collection-Level Audit Tracking
- Link collections and shortcut collections track `lastUpdatedBy` and `lastUpdatedAt` at the collection level
- Audit is updated when:
  - Collection properties change (title, sort order, disabled status)
  - Any link/shortcut within the collection is added, removed, or modified
  - Links or shortcuts are reordered within the collection
- Individual links and shortcuts do not have separate audit fields

### Draft Audit Display
- **New Behavior**: When viewing a draft, ALL audit fields (customization and collection) show values from the draft itself
- When creating a draft from published, audit values are initially copied from the published version
- Draft edits update the audit fields in the draft immediately
- This ensures the UI shows "when was this section last edited" including draft changes
- When a draft is discarded, the system falls back to showing published audit values

## Version Management

The system uses a three-state version management:
- **DRAFT**: Work-in-progress version that can be edited
- **PUBLISHED**: Currently active version visible to users
- **ARCHIVED**: Previous published versions kept for history

## Error Handling

All endpoints return appropriate HTTP status codes:
- **200 OK**: Successful operation
- **404 Not Found**: Resource not found
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **500 Internal Server Error**: Server error

Error responses include descriptive messages to help with debugging.

## Authentication & Authorization

All endpoints require:
1. Valid session authentication
2. Access to the specified handbook's organization
3. Appropriate permissions for the operation (read/write)

The system integrates with the existing LDAP-based authentication and organization-based authorization.