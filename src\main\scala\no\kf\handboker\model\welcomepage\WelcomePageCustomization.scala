package no.kf.handboker.model.welcomepage

case class WelcomePageCustomization(
                                     id: String,
                                     versionId: String,
                                     primaryColor: String,
                                     secondaryColor: String,
                                     welcomeImageUrl: Option[String] = None,
                                     imageTitle: Option[String] = None,
                                     altTitle: Option[String] = None,
                                     imageCredits: Option[String] = None,
                                     welcomeHeader: Option[String] = None,
                                     welcomeText: Option[String] = None,
                                     colorUpdatedBy: Option[String] = None,
                                     colorUpdatedAt: Option[Long] = None,
                                     imageUpdatedBy: Option[String] = None,
                                     imageUpdatedAt: Option[Long] = None,
                                     welcomeHeaderUpdatedBy: Option[String] = None,
                                     welcomeHeaderUpdatedAt: Option[Long] = None,
                                     welcomeTextUpdatedBy: Option[String] = None,
                                     welcomeTextUpdatedAt: Option[Long] = None,
                                     // Added for API responses: base64 data URI of the welcome image so frontend can render without separate fetch
                                     welcomeImageResponse: Option[String] = None
)
