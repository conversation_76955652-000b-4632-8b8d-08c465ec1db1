-- Create the version table
CREATE TABLE welcome_page_version (id VARCHAR(37) PRIMARY KEY, handbook_id VARCHAR(37) NOT NULL REFERENCES handbook(id) ON DELETE CASCADE, status VARCHAR(20) NOT NULL CHECK (status IN ('DRAFT', 'PUBLISHED', 'ARCHIVED')), created_at BIGINT NOT NULL, updated_at BIGINT NOT NULL, created_by VA<PERSON>HA<PERSON>(100), UNIQUE (handbook_id, status));
-- Create the welcome page customization table
CREATE TABLE welcome_page_customization (id VARCHAR(37) PRIMARY KEY, version_id VARCHAR(37) NOT NULL REFERENCES welcome_page_version(id) ON DELETE CASCADE, primary_color VARCHAR(7) NOT NULL, secondary_color VARCHAR(7) NOT NULL, welcome_image VARCHAR(500), image_title VARCHAR(255), alt_title VARCHAR(255), image_credits VARCHAR(255), welcome_header VARCHAR(4000), welcome_text VARCHAR(4000), color_updated_by <PERSON><PERSON><PERSON><PERSON>(100), color_updated_at BIGINT, image_updated_by VA<PERSON><PERSON><PERSON>(100), image_updated_at BIGINT, welcome_header_updated_by VARCHAR(100), welcome_header_updated_at BIGINT, welcome_text_updated_by VARCHAR(100), welcome_text_updated_at BIGINT, UNIQUE (version_id));
-- Create the new link collections table
CREATE TABLE welcome_page_link_collection (id VARCHAR(37) PRIMARY KEY, version_id VARCHAR(37) NOT NULL REFERENCES welcome_page_version(id) ON DELETE CASCADE, title VARCHAR(2000) NOT NULL, sort_order INT NOT NULL DEFAULT 0);
-- Create the new links table
CREATE TABLE welcome_page_link (id VARCHAR(37) PRIMARY KEY, collection_id VARCHAR(37) NOT NULL REFERENCES welcome_page_link_collection(id) ON DELETE CASCADE, title VARCHAR(2000) NOT NULL, url VARCHAR(2000) NOT NULL, description VARCHAR(4000), sort_order INT NOT NULL DEFAULT 0);
-- Create the new shortcut collections table
CREATE TABLE welcome_page_shortcut_collection (id VARCHAR(37) PRIMARY KEY, version_id VARCHAR(37) NOT NULL REFERENCES welcome_page_version(id) ON DELETE CASCADE, title VARCHAR(2000) NOT NULL, disabled SMALLINT DEFAULT 0, sort_order INT NOT NULL DEFAULT 0);
-- Create the new shortcuts table
CREATE TABLE welcome_page_shortcut (id VARCHAR(37) PRIMARY KEY, collection_id VARCHAR(37) NOT NULL REFERENCES welcome_page_shortcut_collection(id) ON DELETE CASCADE, title VARCHAR(2000) NOT NULL, description VARCHAR(4000), sort_order INT NOT NULL DEFAULT 0, link VARCHAR(2000), handbook_id VARCHAR(37), section_id VARCHAR(37), chapter_id VARCHAR(37));
-- 1) Create one PUBLISHED version per non-deleted handbook (if not already exists)
INSERT INTO welcome_page_version (id, handbook_id, status, created_at, updated_at) SELECT gen_random_uuid()::varchar(37),h.id, 'PUBLISHED', (EXTRACT(EPOCH FROM NOW()) * 1000)::bigint,(EXTRACT(EPOCH FROM NOW()) * 1000)::bigint FROM handbook h WHERE h.deleted = 0 AND NOT EXISTS (SELECT 1 FROM welcome_page_version v WHERE v.handbook_id = h.id AND v.status = 'PUBLISHED');

-- 2) Add default customization for each published version without customization
INSERT INTO welcome_page_customization (id, version_id, primary_color, secondary_color, welcome_header, welcome_text, color_updated_at, welcome_header_updated_at, welcome_text_updated_at) SELECT gen_random_uuid()::varchar(37),v.id,'#005A9C','#FFFFFF','Welcome to the Handbook','This is the default welcome message for your handbook.',(EXTRACT(EPOCH FROM NOW()) * 1000)::bigint,(EXTRACT(EPOCH FROM NOW()) * 1000)::bigint,(EXTRACT(EPOCH FROM NOW()) * 1000)::bigint FROM welcome_page_version v LEFT JOIN welcome_page_customization c ON c.version_id = v.id WHERE v.status = 'PUBLISHED' AND c.version_id IS NULL;

-- 3) Migrate link collections and links (if old table exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'handbook_link_collection') THEN

        -- Temporary mapping table
        CREATE TEMP TABLE tmp_collections (old_id VARCHAR(37) PRIMARY KEY,new_id VARCHAR(37) NOT NULL,version_id VARCHAR(37) NOT NULL,title VARCHAR(2000) NOT NULL,sort_order INT NOT NULL);

        -- Fill mapping
        INSERT INTO tmp_collections (old_id, new_id, version_id, title, sort_order) SELECT c.id, gen_random_uuid()::varchar(37),v.id, c.title, COALESCE(c.sort_order::int, 0) FROM handbook_link_collection c JOIN handbook h ON h.id = c.handbook_id JOIN welcome_page_version v ON v.handbook_id = h.id AND v.status = 'PUBLISHED';

        -- Insert new link collections
        INSERT INTO welcome_page_link_collection (id, version_id, title, sort_order) SELECT new_id, version_id, title, sort_order FROM tmp_collections;

        -- Insert links
        INSERT INTO welcome_page_link (id, collection_id, title, url, description, sort_order) SELECT gen_random_uuid()::varchar(37),t.new_id,l.title,l.url,NULL,COALESCE(l.sort_order, 0) FROM handbook_link l JOIN tmp_collections t ON t.old_id = l.handbook_link_collection_id;

        -- Drop temp mapping
        DROP TABLE tmp_collections;

    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX idx_welcome_page_version_handbook ON welcome_page_version(handbook_id);
CREATE INDEX idx_welcome_page_version_status ON welcome_page_version(status);
CREATE INDEX idx_welcome_page_customization_version ON welcome_page_customization(version_id);
CREATE INDEX idx_welcome_page_link_collection_version ON welcome_page_link_collection(version_id);
CREATE INDEX idx_welcome_page_link_collection ON welcome_page_link(collection_id);
CREATE INDEX idx_welcome_page_shortcut_collection_version ON welcome_page_shortcut_collection(version_id);
CREATE INDEX idx_welcome_page_shortcut_collection ON welcome_page_shortcut(collection_id);