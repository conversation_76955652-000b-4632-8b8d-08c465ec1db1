package no.kf.handboker.rest

import no.kf.exception.UserFriendlyException
import no.kf.handboker.model.Image
import no.kf.handboker.model.welcomepage.WelcomePageDto
import no.kf.handboker.rest.support.SessionSupport
import no.kf.rest.ScalatraExceptions
import no.kf.rest.support.{ExternalOrgIdExtractionSupport, JsonSupport}
import org.scalatra.ScalatraServlet
import org.scalatra.servlet.FileUploadSupport
import no.kf.handboker.model.welcomepage.WelcomePageDefaults
import no.kf.handboker.config.{WelcomePageDefaultPrimaryColor, WelcomePageDefaultSecondaryColor, WelcomePageDefaultHeader, WelcomePageDefaultText, WelcomePageDefaultImageUrl, WelcomePageDefaultImageTitle, WelcomePageDefaultImageAltTitle, WelcomePageDefaultImageCredits}

class WelcomePageServlet extends ScalatraServlet with SessionSupport with <PERSON>sonSupport with ExternalOrgIdExtractionSupport with <PERSON>UploadSupport {

  // Override the default formats to include our custom serializers
  override implicit lazy val jsonFormats = WelcomePageDtoFormats.formats

  lazy val welcomePageService = componentRegistry.welcomePageService
  lazy val imageService = componentRegistry.imageService

  /**
   * Get the published welcome page for a handbook
   * GET /api/welcome-page/published/:handbookId
   */
  get("/published/:handbookId/?") {
    val handbookId = params("handbookId")
    welcomePageService.getPublished(handbookId) match {
      case Some(welcomePage) => welcomePage
      case None => ScalatraExceptions.notFound(Some(s"Published welcome page for handbook $handbookId not found."))
    }
  }

  /**
   * Get the draft welcome page for a handbook
   * GET /api/welcome-page/draft/:handbookId
   */
  get("/draft/:handbookId/?") {
    val handbookId = params("handbookId")
    welcomePageService.getDraft(handbookId) match {
      case Some(welcomePage) => welcomePage
      case None => ScalatraExceptions.notFound(Some(s"Draft for handbook $handbookId not found."))
    }
  }

  /**
   * Create a draft welcome page for a handbook
   * POST /api/welcome-page/draft/:handbookId
   * Supports both JSON and multipart/form-data for image upload
   */
  post("/draft/:handbookId/?") {
    val handbookId = params("handbookId")
    val createdBy = currentUser.email

    // Check if this is a multipart request with image upload
    val uploadedImage = getUploadedImageIfPresent

    val draft = welcomePageService.createDraft(handbookId, createdBy)

    // If an image was uploaded, update the draft with the image
    uploadedImage match {
      case Some(image) =>
        welcomePageService.updateWelcomeImage(handbookId, image, createdBy) match {
          case Some(updatedDraft) => updatedDraft
          case None => draft // Fallback to draft without image if update fails
        }
      case None => draft
    }
  }

  /**
   * Update the draft welcome page customization
   * PUT /api/welcome-page/draft/:handbookId
   * Supports both JSON and multipart/form-data for image upload
   */
  put("/draft/:handbookId/?") {
    val handbookId = params("handbookId")
    val lastChangedBy = currentUser.email

    // Check if this is a multipart request with image upload
    val uploadedImage = getUploadedImageIfPresent

    // Handle different content types
    val result = if (isMultipartRequest) {
      // Multipart request - handle image upload and optional JSON data
      handleMultipartUpdate(handbookId, lastChangedBy, uploadedImage)
    } else {
      // Regular JSON request - handle customization update
      val customizationDto = parsedBody.extract[WelcomePageDto]
      welcomePageService.updateCustomization(handbookId, customizationDto, lastChangedBy)
    }

    result match {
      case Some(updatedWelcomePage) => updatedWelcomePage
      case None => ScalatraExceptions.notFound(Some(s"Draft for handbook $handbookId not found to update."))
    }
  }

  /**
   * Discard the draft welcome page
   * DELETE /api/welcome-page/draft/:handbookId
   */
  delete("/draft/:handbookId/?") {
    val handbookId = params("handbookId")

    if (welcomePageService.discardDraft(handbookId)) {
      Map("message" -> s"Draft for handbook $handbookId has been discarded successfully.")
    } else {
      ScalatraExceptions.notFound(Some(s"No draft found for handbook $handbookId to discard."))
    }
  }

  /**
   * Publish the draft welcome page
   * POST /api/welcome-page/publish/:handbookId
   */
  post("/publish/:handbookId/?") {
    val handbookId = params("handbookId")

    welcomePageService.publishDraft(handbookId) match {
      case Some(publishedWelcomePage) => publishedWelcomePage
      case None => ScalatraExceptions.notFound(Some(s"No draft found for handbook $handbookId to publish."))
    }
  }

  /**
   * Get default Welcome Page values from config
   * GET /api/welcome-page/defaults
   */
  get("/defaults/?") {
    // Build image response from configured default image if available
    val defaultImageResponse: Option[String] = componentRegistry.settings
      .optionalSettingFor(WelcomePageDefaultImageUrl)
      .flatMap { url =>
        val maybeFileName = url.split("/").lastOption
        maybeFileName.flatMap { fname =>
          try {
            val file = imageService.retrieveImage(fname)
            val bytes = file.byteArray
            val contentType = file.contentType.getOrElse {
              val ext = fname.split("\\.").lastOption.getOrElse("png")
              s"image/$ext"
            }
            Some(s"data:$contentType;base64,${java.util.Base64.getEncoder.encodeToString(bytes)}")
          } catch {
            case _: Throwable => None
          }
        }
      }

    val defaults = WelcomePageDefaults(
      primaryColor = componentRegistry.settings.settingFor(WelcomePageDefaultPrimaryColor),
      secondaryColor = componentRegistry.settings.settingFor(WelcomePageDefaultSecondaryColor),
      welcomeHeader = componentRegistry.settings.optionalSettingFor(WelcomePageDefaultHeader),
      welcomeText = componentRegistry.settings.optionalSettingFor(WelcomePageDefaultText),
      welcomeImageResponse = defaultImageResponse,
      imageTitle = componentRegistry.settings.optionalSettingFor(WelcomePageDefaultImageTitle),
      altTitle = componentRegistry.settings.optionalSettingFor(WelcomePageDefaultImageAltTitle),
      imageCredits = componentRegistry.settings.optionalSettingFor(WelcomePageDefaultImageCredits)
    )
    defaults
  }

  /**
   * Get welcome page status (whether draft or published exists)
   * GET /api/welcome-page/status/:handbookId
   */
  get("/status/:handbookId/?") {
    val handbookId = params("handbookId")

    val hasDraft = welcomePageService.getDraft(handbookId).isDefined
    val hasPublished = welcomePageService.getPublished(handbookId).isDefined

    Map(
      "handbookId" -> handbookId,
      "hasDraft" -> hasDraft,
      "hasPublished" -> hasPublished
    )
  }

  /**
   * Upload an image for the welcome page draft
   * POST /api/welcome-page/draft/:handbookId/image
   */
  post("/draft/:handbookId/image/?") {
    val handbookId = params("handbookId")
    val updatedBy = currentUser.email

    getUploadedImage match {
      case None => ScalatraExceptions.notFound(Some("No image file provided"))
      case Some(image) =>
        welcomePageService.updateWelcomeImage(handbookId, image, updatedBy) match {
          case Some(updatedWelcomePage) => Map("imageUrl" -> updatedWelcomePage.customization.welcomeImageUrl.getOrElse(""))
          case None => ScalatraExceptions.notFound(Some(s"Draft for handbook $handbookId not found to update image."))
        }
    }
  }

  /**
   * Remove the image from the welcome page draft
   * DELETE /api/welcome-page/draft/:handbookId/image
   */
  delete("/draft/:handbookId/image/?") {
    val handbookId = params("handbookId")
    val updatedBy = currentUser.email

    welcomePageService.removeWelcomeImage(handbookId, updatedBy) match {
      case Some(updatedWelcomePage) => Map("message" -> "Image removed successfully")
      case None => ScalatraExceptions.notFound(Some(s"Draft for handbook $handbookId not found to remove image."))
    }
  }

  /**
   * Extract and validate uploaded image from request (throws exception on validation failure)
   */
  private def getUploadedImage: Option[Image] = {
    if (fileParams.get("file").isEmpty) return None

    val file = fileParams("file")
    val fileSize = file.size
    val maxSizeBytes = 5 * 1024 * 1024 // 5MB

    // Validate file size
    if (fileSize > maxSizeBytes) {
      throw new UserFriendlyException(s"Image file size (${fileSize / 1024 / 1024}MB) exceeds maximum allowed size of 5MB.")
    }

    val contentType = file.contentType.getOrElse({
      throw new UserFriendlyException("Unknown file type.")
    })

    // Validate content type
    if (!contentType.startsWith("image/")) {
      throw new UserFriendlyException("File must be an image.")
    }

    val fileExtension = contentType.split("/").last match {
      case "jpeg" => "jpg"
      case ext => ext
    }

    // Validate supported image formats
    val supportedFormats = Set("jpg", "jpeg", "png", "gif", "webp")
    if (!supportedFormats.contains(fileExtension.toLowerCase)) {
      throw new UserFriendlyException(s"Unsupported image format: $fileExtension. Supported formats: ${supportedFormats.mkString(", ")}")
    }

    Some(Image(fileExtension, file.get()))
  }

  /**
   * Extract uploaded image if present, but don't throw exceptions on validation failure
   * Returns None if no image or validation fails
   */
  private def getUploadedImageIfPresent: Option[Image] = {
    try {
      getUploadedImage
    } catch {
      case _: UserFriendlyException => None // Ignore validation errors for optional image uploads
    }
  }

  /**
   * Check if the current request is a multipart request
   */
  private def isMultipartRequest: Boolean = {
    request.getContentType != null && request.getContentType.toLowerCase.startsWith("multipart/")
  }

  /**
   * Handle multipart update request with optional image upload and JSON data
   */
  private def handleMultipartUpdate(handbookId: String, lastChangedBy: String, uploadedImage: Option[Image]): Option[WelcomePageDto] = {
    // Check if there's JSON data in the request (as a form parameter)
    val jsonData = params.get("data")

    jsonData match {
      case Some(jsonString) =>
        // Parse JSON data and update customization with optional image
        try {
          val customizationDto = org.json4s.jackson.JsonMethods.parse(jsonString).extract[WelcomePageDto]
          welcomePageService.updateCustomizationWithImage(handbookId, customizationDto, uploadedImage, lastChangedBy)
        } catch {
          case _: Exception =>
            // If JSON parsing fails, just handle image upload if present
            uploadedImage match {
              case Some(image) => welcomePageService.updateWelcomeImage(handbookId, image, lastChangedBy)
              case None => welcomePageService.getDraft(handbookId)
            }
        }
      case None =>
        // No JSON data, just handle image upload if present
        uploadedImage match {
          case Some(image) => welcomePageService.updateWelcomeImage(handbookId, image, lastChangedBy)
          case None => welcomePageService.getDraft(handbookId)
        }
    }
  }
}