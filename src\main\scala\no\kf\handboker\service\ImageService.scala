package no.kf.handboker.service

import better.files._
import no.kf.exception.KfException
import no.kf.handboker.config.{AppSettingComponent, ImageFolder}
import no.kf.handboker.model.Image
import no.kf.util.Logging

trait ImageServiceComponent {
  this: AppSettingComponent =>

  val imageService: ImageService

  class ImageServiceImpl extends ImageService with Logging {
    lazy val rootFolder = settings.settingFor(ImageFolder)

    override def persistImage(image: Image): String = {
      write(buildOutputDirectory(image.createdName), image.file)
      s"/handboker/images/${image.createdName}"
    }

    override def persistCoverImage(handbookId: String, versionId: String, fileExtension: String, bytes: Array[Byte]): String = {
      val relPath = s"cover_image/cover_${handbookId}_${versionId}_image.$fileExtension"
      val file = rootFolder / "images" / "cover_image" / s"cover_${handbookId}_${versionId}_image.$fileExtension"
      write(file, bytes)
      s"/handboker/images/$relPath"
    }

    override def retrieveImage(imageName: String): File = {
      val file =
        if (imageName.startsWith("cover_")) {
          // Deterministic welcome page cover image location
          (rootFolder / "images" / "cover_image" / imageName)
        } else {
          buildOutputDirectory(imageName)
        }
      read(file)
    }

    override def deleteImage(imageName: String): Boolean = {
      val file =
        if (imageName.startsWith("cover_")) {
          (rootFolder / "images" / "cover_image" / imageName)
        } else {
          buildOutputDirectory(imageName)
        }
      delete(file)
    }

    private def buildOutputDirectory(imageName: String): File = {
      rootFolder / folderNameFromId(imageName) / imageName
    }

    private def folderNameFromId(id: String) = id.take(2)

    def write(file: File, content: Array[Byte]): Unit = {
      file.createIfNotExists(createParents = true)(File.Attributes.default)
        .writeByteArray(content)(File.OpenOptions.default)
    }

    def read(file: File): File = {
      throwErrorIf(!file.exists, s"Could not find the following file: ${file.path}")
      file
    }

    def delete(file: File): Boolean = {
      if (file.exists) {
        file.delete(false)
        true
      } else {
        log.warn(s"Unable to delete. Could not find file ${file.pathAsString}")
        false
      }
    }

    private def throwErrorIf(func: => Boolean, error: String) = if (func) {
      log.error(error)
      throw new KfException(error)
    }
  }

}

trait ImageService {
  def persistImage(image: Image): String
  def persistCoverImage(handbookId: String, versionId: String, fileExtension: String, bytes: Array[Byte]): String
  def retrieveImage(imageId: String): File
  def deleteImage(imageId: String): Boolean
}
