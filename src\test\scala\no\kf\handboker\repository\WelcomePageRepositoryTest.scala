package no.kf.handboker.repository

import no.kf.handboker.model.welcomepage._
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.scalatest.junit.JUnitRunner
import org.scalatest.{BeforeAndAfterAll, FunSuite}

@RunWith(classOf[JUnitRunner])
class WelcomePageRepositoryTest extends FunSuite with DbTestHandler with BeforeAndAfterAll {

  val repo = componentRegistry.welcomePageRepository

  private val handbookId = "hb-intg-1"

  transactedTest("Create/find/update customization including welcomeImageUrl mapping") {
    // Create draft version
    val draft = repo.createVersion(handbookId, VersionStatus.DRAFT, Some("tester@test"))

    // Create customization
    val now = DateTime.now().getMillis
    val created = repo.createCustomization(
      WelcomePageCustomization(
        id = "",
        versionId = draft.id,
        primaryColor = "#111111",
        secondaryColor = "#222222",
        welcomeImageUrl = Some("/handboker/images/ab.jpg"),
        imageTitle = Some("t"),
        altTitle = Some("a"),
        imageCredits = Some("c"),
        welcomeHeader = Some("hdr"),
        welcomeText = Some("txt"),
        colorUpdatedBy = Some("u1"),
        colorUpdatedAt = Some(now),
        imageUpdatedBy = Some("u2"),
        imageUpdatedAt = Some(now),
        welcomeHeaderUpdatedBy = Some("u3"),
        welcomeHeaderUpdatedAt = Some(now),
        welcomeTextUpdatedBy = Some("u4"),
        welcomeTextUpdatedAt = Some(now)
      )
    )

    val fetched1 = repo.findCustomization(draft.id).get
    assert(fetched1.versionId == draft.id)
    assert(fetched1.welcomeImageUrl.contains("/handboker/images/ab.jpg"))

    // Update customization
    val updated = repo.updateCustomization(
      fetched1.copy(
        primaryColor = "#000000",
        secondaryColor = "#FFFFFF",
        welcomeImageUrl = Some("/handboker/images/xy.png"),
        imageTitle = Some("new"),
        altTitle = None,
        imageCredits = None
      )
    )

    val fetched2 = repo.findCustomization(draft.id).get
    assert(fetched2.primaryColor == "#000000")
    assert(fetched2.secondaryColor == "#FFFFFF")
    assert(fetched2.welcomeImageUrl.contains("/handboker/images/xy.png"))
    assert(fetched2.imageTitle.contains("new"))
    assert(fetched2.altTitle.isEmpty)
  }

  transactedTest("Link collections and links CRUD and ordering") {
    val draft = repo.createVersion(handbookId, VersionStatus.DRAFT, Some("tester@test"))
    repo.createCustomization(WelcomePageCustomization("", draft.id, "#111111", "#222222"))

    val lc1 = repo.createLinkCollection(draft.id, "Links A", 2)
    val lc2 = repo.createLinkCollection(draft.id, "Links B", 1)

    val l1 = repo.createLink(lc2.id, "B1", "https://b1", Some("d1"), 1)
    val l2 = repo.createLink(lc2.id, "B2", "https://b2", None, 2)

    val collections = repo.findLinkCollections(draft.id)
    assert(collections.map(_.title) == Seq("Links B", "Links A"))

    val links = repo.findLinks(lc2.id)
    assert(links.map(_.title) == Seq("B1", "B2"))

    // Update collection
    val lc2u = repo.updateLinkCollection(lc2.copy(title = "Links Bx", sortOrder = 3))
    val collections2 = repo.findLinkCollections(draft.id)
    assert(collections2.map(_.title) == Seq("Links A", "Links Bx"))

    // Delete all collections
    repo.deleteLinkCollections(draft.id)
    assert(repo.findLinkCollections(draft.id).isEmpty)
  }

  transactedTest("Shortcut collections and shortcuts CRUD and ordering") {
    val draft = repo.createVersion(handbookId, VersionStatus.DRAFT, Some("tester@test"))
    repo.createCustomization(WelcomePageCustomization("", draft.id, "#111111", "#222222"))

    val sc1 = repo.createShortcutCollection(draft.id, "Shortcuts A", disabled = false, 2)
    val sc2 = repo.createShortcutCollection(draft.id, "Shortcuts B", disabled = true, 1)

    val s1 = repo.createShortcut(WelcomePageShortcut("", sc2.id, "S1", Some("d1"), 1, Some("/link1"), None, None, None))
    val s2 = repo.createShortcut(WelcomePageShortcut("", sc2.id, "S2", None, 2, None, Some("hb"), Some("sec"), Some("chap")))

    val collections = repo.findShortcutCollections(draft.id)
    assert(collections.map(_.title) == Seq("Shortcuts B", "Shortcuts A"))
    assert(collections.head.disabled == 1)

    val shortcuts = repo.findShortcuts(sc2.id)
    assert(shortcuts.map(_.title) == Seq("S1", "S2"))

    // Update collection
    val sc2u = repo.updateShortcutCollection(sc2.copy(title = "Shortcuts Bx", disabled = 0, sortOrder = 3))
    val collections2 = repo.findShortcutCollections(draft.id)
    assert(collections2.map(_.title) == Seq("Shortcuts A", "Shortcuts Bx"))

    // Delete all collections
    repo.deleteShortcutCollections(draft.id)
    assert(repo.findShortcutCollections(draft.id).isEmpty)
  }

  transactedTest("Publish draft archives previous published and promotes draft") {
    val published = repo.createVersion(handbookId, VersionStatus.PUBLISHED, Some("u"))
    repo.createCustomization(WelcomePageCustomization("", published.id, "#111", "#222"))

    val draft = repo.createVersion(handbookId, VersionStatus.DRAFT, Some("u"))
    repo.createCustomization(WelcomePageCustomization("", draft.id, "#333", "#444"))

    repo.publishVersion(draft.id, Some(published.id))

    val pubAfter = repo.findVersion(handbookId, VersionStatus.PUBLISHED)
    val archived = repo.findVersion(handbookId, VersionStatus.ARCHIVED)

    assert(pubAfter.isDefined && pubAfter.get.id == draft.id)
    assert(archived.isDefined && archived.get.id == published.id)
  }

  transactedTest("deleteVersion cascades to customization, collections, links, shortcuts") {
    val draft = repo.createVersion(handbookId, VersionStatus.DRAFT, Some("u"))
    repo.createCustomization(WelcomePageCustomization("", draft.id, "#111", "#222"))

    val lc = repo.createLinkCollection(draft.id, "L", 1)
    repo.createLink(lc.id, "L1", "https://l1", None, 1)

    val sc = repo.createShortcutCollection(draft.id, "S", disabled = false, 1)
    repo.createShortcut(WelcomePageShortcut("", sc.id, "S1", None, 1, None, None, None, None))

    // Sanity
    assert(repo.findCustomization(draft.id).isDefined)
    assert(repo.findLinkCollections(draft.id).nonEmpty)
    assert(repo.findShortcutCollections(draft.id).nonEmpty)

    // Delete
    repo.deleteVersion(draft.id)

    assert(repo.findCustomization(draft.id).isEmpty)
    assert(repo.findLinkCollections(draft.id).isEmpty)
    assert(repo.findShortcutCollections(draft.id).isEmpty)
  }
}

